<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prayer Times</title>
    <link href="https://fonts.googleapis.com/css2?family=El+Messiri:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
/* ============ BASE STYLES ============ */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: linear-gradient(135deg, #0d1b3a, #1c3b5a, #152a43);
    color: #fff;
    min-height: 100vh;
    padding: 20px;
    position: relative;
    overflow-x: hidden;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: float 15s infinite linear;
    z-index: -1;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

/* ============ HEADER STYLES ============ */
.header {
    text-align: center;
    padding: 30px 0;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    animation: fadeIn 1s ease-out;
}

.header h1 {
    font-size: 3.5rem;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #fff, #a8edea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.arabic {
    font-family: 'El Messiri', 'Segoe UI', serif;
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.9);
}

.subtitle {
    font-size: 1.3rem;
    opacity: 0.85;
    max-width: 600px;
    margin: 0 auto;
    color: #a8edea;
}

/* ============ CARD & CONTROLS ============ */
.card {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    animation: fadeInUp 0.8s ease-out;
}

.controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 1rem;
    color: #a8edea;
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-group input, .input-group select {
    padding: 14px 18px;
    border-radius: 12px;
    border: 2px solid rgba(168, 237, 234, 0.3);
    background: rgba(0, 0, 0, 0.2);
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.input-group input:focus, .input-group select:focus {
    outline: none;
    border-color: #a8edea;
    box-shadow: 0 0 0 4px rgba(168, 237, 234, 0.2);
    background: rgba(0, 0, 0, 0.3);
}

.btn-group {
    display: flex;
    gap: 15px;
    align-items: flex-end;
}

.btn {
    padding: 14px 25px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #00c6ff, #0072ff);
    color: white;
    box-shadow: 0 5px 15px rgba(0, 114, 255, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #ff6b6b, #ff416c);
    color: white;
    box-shadow: 0 5px 15px rgba(255, 65, 108, 0.3);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* ============ PRAYER GRID ============ */
.prayer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 25px;
}

.prayer-card {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 25px;
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: slideIn 0.6s ease-out;
}

.prayer-card.current {
    background: linear-gradient(135deg, rgba(0, 114, 255, 0.3), rgba(0, 198, 255, 0.3));
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 114, 255, 0.3);
}

.prayer-card.temporal {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 65, 108, 0.2));
}

.prayer-card.day-temporal {
    background: linear-gradient(135deg, rgba(245, 166, 35, 0.2), rgba(255, 203, 5, 0.3));
}

.prayer-card.temporal.temporal-night {
    background: linear-gradient(135deg, rgba(41, 128, 185, 0.3), rgba(109, 33, 79, 0.3));
}

.prayer-card.temporal.temporal-night .prayer-name {
    color: #a8edea;
}

.prayer-card.temporal.temporal-night .temporal-time {
    color: #B8E986;
}

.prayer-card.temporal.temporal-night .time-until {
    background: rgba(0, 0, 0, 0.3);
    color: #a8edea;
}

.prayer-name {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.prayer-time {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 15px 0;
    color: #a8edea;
}

.temporal-time {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 15px 0;
    color: #ffcc00;
}

.time-until {
    font-size: 1.1rem;
    background: rgba(0, 0, 0, 0.2);
    padding: 8px 15px;
    border-radius: 20px;
    display: inline-block;
    margin-top: 10px;
    color: #ffcc00;
}

.prayer-stats {
    margin-top: 10px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.15);
    border-radius: 10px;
    font-size: 0.9rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 3px 0;
}

.stat-label {
    color: #a8edea;
}

.stat-value {
    font-weight: 500;
}

/* ============ DATE DISPLAY ============ */
.date-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    margin: 20px 0;
}

.date-item {
    text-align: center;
    flex: 1;
}

.date-label {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.date-value {
    font-size: 1.4rem;
    font-weight: 600;
}

.hijri-date {
    color: #a8edea;
}

/* ============ CHART CONTAINER ============ */
.chart-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 25px;
    margin-top: 30px;
    height: 400px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 1.4rem;
    font-weight: 600;
}

.chart-toggle {
    display: flex;
    gap: 12px;
}

.toggle-btn {
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.toggle-btn.active {
    background: linear-gradient(135deg, #00c6ff, #0072ff);
    border-color: rgba(255, 255, 255, 0.4);
}

/* ============ STATUS MESSAGES ============ */
.loading {
    text-align: center;
    padding: 40px;
    font-size: 1.4rem;
    color: #a8edea;
}

.error {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 65, 108, 0.2));
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    margin: 20px 0;
    border: 1px solid rgba(255, 65, 108, 0.3);
}

.chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #a8edea;
    font-size: 1.2rem;
}

/* ============ ICON FIXES ============ */
.prayer-name i {
    font-size: 1.2rem;
    min-width: 24px;
    display: inline-block;
    text-align: center;
}

.temporal .prayer-name i {
    font-size: 1.4rem;
}

/* ============ SPECIAL TIMES ============ */
.night-special-times, .day-special-times {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.night-time-item, .day-time-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.15);
    border-radius: 12px;
}

.night-time-label, .day-time-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

.night-time-value, .day-time-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #B8E986;
}

.duha-window {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.duha-time {
    text-align: center;
    padding: 5px 7px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    min-width: 50px;
}

.duha-time-label {
    font-size: 0.7rem;
    color: #a8edea;
    margin-bottom: 5px;
}

.duha-time-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #ffcc00;
}

.preferred-time {
    background: linear-gradient(135deg, rgba(245, 166, 35, 0.3), rgba(255, 203, 5, 0.4));
    border: 1px solid rgba(245, 166, 35, 0.5);
}

.chart-note {
    text-align: center;
    margin-top: 10px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.6);
}

/* ============ ICON STYLES ============ */
.prayer-icon {
    display: inline-block;
    width: 28px;
    height: 28px;
    position: relative;
    margin-right: 10px;
    vertical-align: middle;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* FAJR */
.icon-fajr {
    background: linear-gradient(180deg, 
        #0d1b3a 0%, 
        #1a2745 15%, 
        #2d4a6b 35%, 
        #4a7c7e 60%,
        #6b9b9d 75%,
        #a8edea 88%, 
        #f0f9ff 100%);
    border-radius: 50%;
    position: relative;
    box-shadow: 
        inset 0 2px 4px rgba(168, 237, 234, 0.3),
        0 4px 8px rgba(13, 27, 58, 0.4);
}

.icon-fajr::before {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 22px;
    height: 2px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(168, 237, 234, 0.4) 10%,
        rgba(168, 237, 234, 0.9) 50%, 
        rgba(168, 237, 234, 0.4) 90%,
        transparent 100%);
    border-radius: 2px;
    box-shadow: 0 0 8px rgba(168, 237, 234, 0.6);
}

.icon-fajr::after {
    content: '';
    position: absolute;
    bottom: 6px;
    left: 50%;
    transform: translateX(-50%);
    width: 0.5px;
    height: 12px;
    background: linear-gradient(0deg, 
        rgba(168, 237, 234, 0.8) 0%, 
        rgba(168, 237, 234, 0.4) 70%,
        transparent 100%);
    border-radius: 1px;
    box-shadow: 
        -8px 0 0 0 rgba(168, 237, 234, 0.6),
        -4px 0 0 0 rgba(168, 237, 234, 0.7),
        4px 0 0 0 rgba(168, 237, 234, 0.7),
        8px 0 0 0 rgba(168, 237, 234, 0.6),
        -6px -2px 0 0 rgba(168, 237, 234, 0.5),
        6px -2px 0 0 rgba(168, 237, 234, 0.5);
}

/* SUNRISE */
.icon-sunrise {
    background: radial-gradient(circle at center, 
        #ffffff 20%, 
        #fff59d 40%, 
        #ffca28 65%, 
        #ff8f00 90%);
    border-radius: 50%;
    position: relative;
    box-shadow: 
        inset 0 2px 4px rgba(255, 255, 255, 0.3),
        0 0 16px rgba(255, 202, 40, 0.5),
        0 4px 12px rgba(255, 143, 0, 0.3);
}

/* DHUHR */
.icon-dhuhr {
    background: radial-gradient(circle at center, 
        #ffffff 20%, 
        #fff59d 40%, 
        #ffca28 65%, 
        #ff8f00 100%);
    border-radius: 50%;
    position: relative;
    box-shadow: 
        inset 0 2px 6px rgba(255, 255, 255, 0.5),
        0 0 24px rgba(255, 202, 40, 0.7),
        0 0 40px rgba(255, 143, 0, 0.3);
}

.icon-dhuhr::before {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    background: conic-gradient(from 0deg, 
        rgba(255, 202, 40, 0.8) 0deg, 
        transparent 30deg, 
        rgba(255, 202, 40, 0.8) 60deg, 
        transparent 90deg, 
        rgba(255, 202, 40, 0.8) 120deg, 
        transparent 150deg, 
        rgba(255, 202, 40, 0.8) 180deg, 
        transparent 190deg,
        rgba(255, 202, 40, 0.8) 240deg, 
        transparent 270deg,
        rgba(255, 202, 40, 0.8) 300deg, 
        transparent 330deg, 
        rgba(255, 202, 40, 0.8) 360deg);
    border-radius: 50%;
}

.icon-dhuhr::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, 
        rgba(255, 255, 255, 1) 0%, 
        rgba(255, 255, 255, 0.9) 30%,
        rgba(255, 255, 255, 0.4) 60%,
        transparent 80%);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

/* ASR */
.icon-asr {
    background: linear-gradient(135deg, 
        #ffecb3 0%, 
        #ffca28 25%, 
        #ff8f00 65%, 
        #e65100 100%);
    border-radius: 50%;
    position: relative;
    box-shadow: 
        inset 0 2px 4px rgba(255, 255, 255, 0.2),
        0 0 12px rgba(255, 143, 0, 0.4),
        0 6px 16px rgba(230, 81, 0, 0.25);
}

.icon-asr::before {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    background: conic-gradient(from 45deg, 
        transparent 0deg, 
        rgba(255, 202, 40, 0.35) 20deg, 
        transparent 40deg,
        rgba(255, 202, 40, 0.35) 80deg,
        transparent 100deg,
        rgba(255, 202, 40, 0.35) 140deg,
        transparent 160deg,
        rgba(255, 202, 40, 0.35) 200deg,
        transparent 220deg,
        rgba(255, 202, 40, 0.35) 260deg,
        transparent 280deg,
        rgba(255, 202, 40, 0.35) 320deg,
        transparent 340deg,
        rgba(255, 202, 40, 0.35) 360deg);
    border-radius: 50%;
}

.icon-asr::after {
    content: '';
    position: absolute;
    bottom: -6px;
    right: -4px;
    width: 20px;
    height: 8px;
    background: linear-gradient(135deg, 
        rgba(0, 0, 0, 0.3) 0%, 
        rgba(0, 0, 0, 0.15) 50%, 
        transparent 100%);
    border-radius: 50%;
    transform: rotate(-15deg) skewX(-20deg);
    filter: blur(1px);
}

/* MAGHRIB */
.icon-maghrib {
    background: linear-gradient(180deg, 
        #ff6b35 0%, 
        #ff7043 10%,
        #ff8a50 25%, 
        #ffab40 40%,
        #ffb74d 55%,
        #ffcc02 70%,
        #c8e6c9 85%, 
        #37474f 100%);
    border-radius: 50%;
    position: relative;
    box-shadow: 
        inset 0 4px 8px rgba(255, 183, 77, 0.3),
        0 0 20px rgba(255, 112, 67, 0.5);
}

.icon-maghrib::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 65%;
    background: linear-gradient(180deg, 
        transparent 0%, 
        rgba(200, 230, 201, 0.1) 15%,
        rgba(55, 71, 79, 0.1) 30%,
        rgba(55, 71, 79, 0.25) 50%,
        rgba(55, 71, 79, 0.5) 70%, 
        rgba(55, 71, 79, 0.8) 100%);
    border-radius: 0 0 50% 50%;
}

.icon-maghrib::after {
    content: '';
    position: absolute;
    top: 4px;
    left: 4px;
    right: 4px;
    height: 12px;
    background: 
        radial-gradient(ellipse at center, 
            rgba(255, 255, 255, 1) 0%,
            rgba(255, 213, 79, 0.9) 20%, 
            rgba(255, 171, 64, 0.7) 40%,
            rgba(255, 138, 80, 0.5) 60%, 
            transparent 80%),
        linear-gradient(180deg,
            transparent 50%,
            rgba(255, 138, 80, 0.3) 70%,
            rgba(255, 112, 67, 0.2) 100%);
    border-radius: 50%;
    box-shadow: 0 0 12px rgba(255, 183, 77, 0.7);
}

/* ISHA */
.icon-isha {
    background: radial-gradient(circle at 30% 30%, 
        #2c3e50 0%, 
        #1c3b5a 50%, 
        #0d1b3a 100%);
    border-radius: 50%;
    position: relative;
    box-shadow: 
        inset 0 2px 6px rgba(168, 237, 234, 0.15),
        0 0 15px rgba(13, 27, 58, 0.7);
    overflow: hidden;
}

.icon-isha::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 18px;
    height: 18px;
    background: 
        radial-gradient(circle at 20% 20%, 
            #ffffff 0%, 
            #f8f9fa 25%,
            #e3f2fd 45%, 
            #a8edea 70%, 
            rgba(168, 237, 234, 0.8) 100%),
        radial-gradient(circle at 35% 40%, 
            rgba(0, 0, 0, 0.15) 0%, 
            transparent 30%),
        radial-gradient(circle at 60% 25%, 
            rgba(0, 0, 0, 0.12) 0%, 
            transparent 25%),
        radial-gradient(circle at 25% 65%, 
            rgba(0, 0, 0, 0.08) 0%, 
            transparent 20%);
    border-radius: 50%;
    box-shadow: 
        0 0 12px rgba(168, 237, 234, 0.8),
        inset 2px 2px 4px rgba(255, 255, 255, 0.6),
        inset -1px -1px 3px rgba(0, 0, 0, 0.15);
}

.icon-isha::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-25%, -50%);
    width: 16px;
    height: 16px;
    background: radial-gradient(circle at center, 
        #0d1b3a 0%, 
        #1c3b5a 70%, 
        transparent 100%);
    border-radius: 50%;
}

.icon-isha {
    background-image: 
        radial-gradient(circle at 30% 30%, #2c3e50 0%, #1c3b5a 50%, #0d1b3a 100%),
        radial-gradient(1.5px 1.5px at 78% 18%, #ffffff 0%, transparent 50%),
        radial-gradient(1.2px 1.2px at 82% 65%, rgba(168, 237, 234, 0.9) 0%, transparent 50%),
        radial-gradient(0.9px 0.9px at 20% 75%, rgba(255, 255, 255, 0.8) 0%, transparent 50%),
        radial-gradient(1.3px 1.3px at 15% 25%, rgba(168, 237, 234, 0.85) 0%, transparent 50%),
        radial-gradient(1.4px 1.4px at 88% 40%, #ffffff 0%, transparent 50%),
        radial-gradient(0.8px 0.8px at 25% 15%, rgba(255, 255, 255, 0.7) 0%, transparent 50%),
        radial-gradient(1.1px 1.1px at 70% 12%, rgba(168, 237, 234, 0.8) 0%, transparent 50%),
        radial-gradient(1px 1px at 45% 88%, rgba(255, 255, 255, 0.85) 0%, transparent 50%);
    background-size: 100% 100%, 3px 3px, 3px 3px, 2px 2px, 3px 3px, 3px 3px, 2px 2px, 3px 3px, 2px 2px;
    background-repeat: no-repeat;
}

/* DAY HOUR */
.icon-day-hour {
    background: radial-gradient(circle at center, 
        #ffffff 10%, 
        #fff176 30%, 
        #ffca28 60%, 
        #ff8f00 100%);
    border-radius: 50%;
    position: relative;
    box-shadow: 
        inset 0 2px 6px rgba(255, 255, 255, 0.5),
        0 0 28px rgba(255, 202, 40, 0.8),
        0 0 50px rgba(255, 143, 0, 0.4);
}

.icon-day-hour::before {
    content: '';
    position: absolute;
    top: -12px;
    left: -12px;
    right: -12px;
    bottom: -12px;
    background: conic-gradient(from 0deg, 
        rgba(255, 202, 40, 0.8) 0deg, 
        transparent 10deg, 
        rgba(255, 202, 40, 0.8) 20deg, 
        transparent 30deg,
        rgba(255, 202, 40, 0.8) 40deg, 
        transparent 50deg, 
        rgba(255, 202, 40, 0.8) 60deg, 
        transparent 70deg,
        rgba(255, 202, 40, 0.8) 80deg, 
        transparent 90deg, 
        rgba(255, 202, 40, 0.8) 100deg, 
        transparent 110deg,
        rgba(255, 202, 40, 0.8) 120deg, 
        transparent 130deg, 
        rgba(255, 202, 40, 0.8) 140deg, 
        transparent 150deg,
        rgba(255, 202, 40, 0.8) 160deg, 
        transparent 170deg, 
        rgba(255, 202, 40, 0.8) 180deg, 
        transparent 190deg,
        rgba(255, 202, 40, 0.8) 200deg, 
        transparent 210deg, 
        rgba(255, 202, 40, 0.8) 220deg, 
        transparent 230deg,
        rgba(255, 202, 40, 0.8) 240deg, 
        transparent 250deg, 
        rgba(255, 202, 40, 0.8) 260deg, 
        transparent 270deg,
        rgba(255, 202, 40, 0.8) 280deg, 
        transparent 290deg, 
        rgba(255, 202, 40, 0.8) 300deg, 
        transparent 310deg,
        rgba(255, 202, 40, 0.8) 320deg, 
        transparent 330deg, 
        rgba(255, 202, 40, 0.8) 340deg, 
        transparent 350deg,
        rgba(255, 202, 40, 0.8) 360deg);
    border-radius: 50%;
}

/* NIGHT HOUR */
.icon-night-hour {
    width:35px;
    height:35px;
    background: radial-gradient(circle at 40% 30%, 
        #34495e 0%, 
        #2c3e50 40%, 
        #1c3b5a 100%);
    border-radius: 50%;
    position: relative;
    box-shadow: 
        inset 0 2px 4px rgba(168, 237, 234, 0.1),
        0 0 16px rgba(28, 59, 90, 0.6);
}

.icon-night-hour::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, 
        transparent 0%,
        transparent 25%, 
        rgba(168, 237, 234, 0.3) 30%,
        rgba(168, 237, 234, 0.9) 45%, 
        #ffffff 55%,
        #f0f9ff 65%,
        rgba(168, 237, 234, 0.9) 75%,
        rgba(168, 237, 234, 0.3) 85%,
        transparent 90%,
        transparent 100%);
    border-radius: 50%;
    clip-path: circle(50% at 35% 50%);
    box-shadow: 
        0 0 12px rgba(168, 237, 234, 0.4),
        inset -1px -1px 3px rgba(0, 0, 0, 0.2);
}

.icon-night-hour::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 6px;
    width: 2px;
    height: 2px;
    background: #ffffff;
    border-radius: 50%;
    box-shadow: 
        -8px -4px 0 0 rgba(255, 255, 255, 0.8),
        -18px 21px 0 -0.5px rgba(168, 237, 234, 0.6),
        4px 14px 0 -0.4px rgba(255, 255, 255, 0.8),
        -20px 24px 0 -0.75px rgba(168, 237, 234, 0.4);
}

/* MIDNIGHT */
.icon-midnight {
    background: radial-gradient(circle at center, 
        #34495e 20%, 
        #2c3e50 60%, 
        #1c3b5a 100%);
    border-radius: 50%;
    border: 2px solid rgba(168, 237, 234, 0.5);
    position: relative;
    box-shadow: 
        inset 0 2px 4px rgba(52, 73, 94, 0.4),
        0 0 15px rgba(28, 59, 90, 0.5);
}

.icon-midnight::before {
    content: '';
    position: absolute;
    top: 6px;
    left: 50%;
    transform: translateX(-50%);
    width: 3px;
    height: 8px;
    background: linear-gradient(180deg, 
        #ffffff 0%, 
        #a8edea 70%,
        rgba(168, 237, 234, 0.8) 100%);
    border-radius: 2px 2px 0 0;
    box-shadow: 0 0 6px rgba(168, 237, 234, 0.7);
    z-index: 2;
}

.icon-midnight::after {
    content: '';
    position: absolute;
    top: 3px;
    left: 50%;
    transform: translateX(-50%);
    width: 1.5px;
    height: 11px;
    background: linear-gradient(180deg, 
        #ffffff 0%, 
        #a8edea 80%,
        rgba(168, 237, 234, 0.7) 100%);
    border-radius: 1px 1px 0 0;
    box-shadow: 0 0 4px rgba(168, 237, 234, 0.6);
    z-index: 1;
}

.icon-midnight {
    background-image: 
        radial-gradient(circle at center, #34495e 20%, #2c3e50 60%, #1c3b5a 100%),
        linear-gradient(0deg, transparent 48%, rgba(168, 237, 234, 0.9) 48%, rgba(168, 237, 234, 0.9) 52%, transparent 52%),
        linear-gradient(90deg, transparent 48%, rgba(168, 237, 234, 0.6) 48%, rgba(168, 237, 234, 0.6) 52%, transparent 52%),
        linear-gradient(180deg, transparent 48%, rgba(168, 237, 234, 0.6) 48%, rgba(168, 237, 234, 0.6) 52%, transparent 52%),
        linear-gradient(270deg, transparent 48%, rgba(168, 237, 234, 0.6) 48%, rgba(168, 237, 234, 0.6) 52%, transparent 52%),
        radial-gradient(circle at 50% 50%, #ffffff 0%, #ffffff 25%, transparent 26%);
    background-size: 100% 100%, 4px 28px, 28px 4px, 4px 28px, 28px 4px, 6px 6px;
    background-position: center, center, center, center, center, center;
    background-repeat: no-repeat;
}

/* LAST THIRD */
.icon-last-third {
    background: linear-gradient(150deg, 
        #1c3b5a 0%, 
        #2c3e50 30%, 
        #34495e 60%, 
        #4a6373 85%,
        rgba(168, 237, 234, 0.25) 100%);
    border-radius: 50%;
    position: relative;
    box-shadow: 
        inset 0 2px 6px rgba(168, 237, 234, 0.2),
        0 0 22px rgba(28, 59, 90, 0.6);
    overflow: hidden;
}

.icon-last-third::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 45%;
    transform: translate(-50%, -50%);
    width: 14px;
    height: 14px;
    background: 
        radial-gradient(circle at 30% 25%, 
            #ffffff 0%, 
            #f8f9fa 20%,
            #e8f4f8 40%, 
            #a8edea 75%, 
            rgba(168, 237, 234, 0.7) 100%);
    border-radius: 50%;
    box-shadow: 
        0 0 10px rgba(168, 237, 234, 0.5),
        inset 1px 1px 3px rgba(255, 255, 255, 0.5);
}

.icon-last-third::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 60%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background: radial-gradient(circle at center, 
        #34495e 0%, 
        rgba(52, 73, 94, 0.9) 60%, 
        transparent 100%);
    border-radius: 50%;
}

.icon-last-third {
    background-image: 
        linear-gradient(150deg, #1c3b5a 0%, #2c3e50 30%, #34495e 60%, #4a6373 85%, rgba(168, 237, 234, 0.25) 100%),
        linear-gradient(0deg, rgba(168, 237, 234, 0.3) 0%, transparent 40%),
        radial-gradient(1px 1px at 25% 20%, rgba(255, 255, 255, 0.8) 0%, transparent 50%),
        radial-gradient(0.7px 0.7px at 75% 15%, rgba(168, 237, 234, 0.7) 0%, transparent 50%),
        radial-gradient(0.8px 0.8px at 15% 70%, rgba(255, 255, 255, 0.6) 0%, transparent 50%),
        radial-gradient(1.1px 1.1px at 85% 65%, rgba(168, 237, 234, 0.5) 0%, transparent 50%),
        radial-gradient(0.6px 0.6px at 20% 40%, rgba(255, 255, 255, 0.5) 0%, transparent 50%);
    background-size: 100% 100%, 100% 30%, 2px 2px, 2px 2px, 2px 2px, 2px 2px, 2px 2px;
    background-position: center, bottom, center, center, center, center, center;
    background-repeat: no-repeat;
}

/* DUHA */
.icon-duha {
    background: linear-gradient(45deg, 
        #fff8e1 0%, 
        #ffecb3 20%,
        #ffe082 40%, 
        #ffca28 70%, 
        #ff8f00 100%);
    border-radius: 50%;
    position: relative;
    box-shadow: 
        inset 0 2px 6px rgba(255, 255, 255, 0.4),
        0 0 18px rgba(255, 204, 2, 0.5),
        0 4px 12px rgba(255, 143, 0, 0.3);
}

.icon-duha::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    background: radial-gradient(circle at 30% 30%, 
        rgba(255, 255, 255, 0.9) 0%, 
        rgba(255, 255, 255, 0.4) 35%, 
        transparent 65%);
    border-radius: 50%;
}

.icon-duha::after {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    background: conic-gradient(from 0deg, 
        transparent 0deg, 
        rgba(255, 204, 2, 0.4) 45deg, 
        transparent 90deg,
        rgba(255, 204, 2, 0.4) 135deg,
        transparent 180deg,
        rgba(255, 204, 2, 0.4) 225deg,
        transparent 270deg,
        rgba(255, 204, 2, 0.4) 315deg,
        transparent 360deg);
    border-radius: 50%;
}

/* ============ ANIMATIONS ============ */
@keyframes float {
    0% { transform: translateY(100vh) rotate(0deg); }
    100% { transform: translateY(-10vh) rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes rotate-rays {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes gentle-pulse {
    0%, 100% { 
        box-shadow: 0 0 20px rgba(168, 237, 234, 0.4);
        transform: scale(1);
    }
    50% { 
        box-shadow: 0 0 30px rgba(168, 237, 234, 0.8);
        transform: scale(1.05);
    }
}

@keyframes twinkle {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 1; }
}

@keyframes gentle-glow {
    0%, 100% { 
        filter: brightness(1);
        box-shadow: inset 0 2px 4px rgba(168, 237, 234, 0.1), 0 0 16px rgba(28, 59, 90, 0.6);
    }
    50% { 
        filter: brightness(1.05);
        box-shadow: inset 0 2px 4px rgba(168, 237, 234, 0.15), 0 0 20px rgba(28, 59, 90, 0.8);
    }
}

/* ============ INTERACTIONS ============ */
.prayer-icon:hover {
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.temporal .prayer-icon {
    filter: brightness(1.1);
}

.icon-dhuhr:hover::before {
    animation: rotate-rays 20s linear infinite;
}

.icon-day-hour:hover::before {
    animation: rotate-rays 15s linear infinite;
}

.icon-night-hour:hover {
    animation: gentle-glow 4s ease-in-out infinite;
}

.icon-night-hour:hover::after {
    animation: twinkle 3s ease-in-out infinite;
}

.preferred-time .prayer-icon {
    box-shadow: 0 0 20px rgba(46, 204, 113, 0.6);
    filter: brightness(1.15);
}

/* ============ RESPONSIVE DESIGN ============ */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2.5rem;
    }
    
    .arabic {
        font-size: 1.8rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .date-display {
        flex-direction: column;
        gap: 15px;
    }
    
    .chart-toggle {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .prayer-stats {
        font-size: 0.8rem;
    }
    
    .temporal-time {
        font-size: 1.6rem;
    }
    
    .night-time-value, .day-time-value {
        font-size: 1.1rem;
    }
    
    .duha-window {
        flex-direction: column;
        gap: 7px;
    }
    
    .duha-time {
        width: 100%;
    }
    
    .prayer-icon {
        width: 24px;
        height: 24px;
    }
}

@media (max-width: 480px) {
    .prayer-icon {
        width: 22px;
        height: 22px;
    }
}

/* ============ ACCESSIBILITY ============ */
@media (prefers-reduced-motion: reduce) {
    .prayer-icon,
    .prayer-icon::before,
    .prayer-icon::after {
        animation: none !important;
    }
}

@media (prefers-contrast: high) {
    .prayer-icon {
        border: 1px solid currentColor;
    }
    
    .icon-isha::before,
    .icon-night-hour::before,
    .icon-last-third::before {
        border: 1px solid rgba(255, 255, 255, 0.8);
    }
}

/* ============ FOOTER ============ */
.footer {
    text-align: center;
    padding: 20px 0;
    margin-top: 40px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-text {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.footer-text a {
    color: #a8edea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-text a:hover {
    color: #ffffff;
    text-decoration: underline;
}
    </style>
</head>
<body>
    <!-- Background particles -->
    <div class="particle" style="width: 6px; height: 6px; left: 5%; animation-delay: 0s;"></div>
    <div class="particle" style="width: 8px; height: 8px; left: 15%; animation-delay: 2s;"></div>
    <div class="particle" style="width: 5px; height: 5px; left: 25%; animation-delay: 4s;"></div>
    <div class="particle" style="width: 7px; height: 7px; left: 35%; animation-delay: 1s;"></div>
    <div class="particle" style="width: 4px; height: 4px; left: 45%; animation-delay: 3s;"></div>
    <div class="particle" style="width: 9px; height: 9px; left: 55%; animation-delay: 5s;"></div>
    <div class="particle" style="width: 6px; height: 6px; left: 65%; animation-delay: 2.5s;"></div>
    <div class="particle" style="width: 5px; height: 5px; left: 75%; animation-delay: 1.5s;"></div>
    <div class="particle" style="width: 7px; height: 7px; left: 85%; animation-delay: 3.5s;"></div>
    <div class="particle" style="width: 4px; height: 4px; left: 95%; animation-delay: 0.5s;"></div>

    <div class="container">
        <div class="header">
            <h1>Prayer Times</h1>
            <div class="arabic">مواقيت الصلاة</div>
            <p class="subtitle">Prayer Times, Temporal Hours & Comparative Method Analysis</p>
        </div>
        
        <div class="card">
            <div class="controls">
                <div class="input-group">
                    <label for="city"><i class="fas fa-city"></i> City</label>
                    <input type="text" id="city" placeholder="Enter your city" value="Mecca">
                </div>
                
                <div class="input-group">
                    <label for="country"><i class="fas fa-globe"></i> Country</label>
                    <input type="text" id="country" placeholder="Enter your country" value="Saudi Arabia">
                </div>
                
                <div class="input-group">
                    <label for="method"><i class="fas fa-calculator"></i> Calculation Method</label>
                    <select id="method">
    			<option value="0">Jafari / Shia Ithna-Ashari</option>
    			<option value="1">University of Islamic Sciences, Karachi</option>
    			<option value="2">Islamic Society of North America</option>
    			<option value="3">Muslim World League</option>
    			<option value="4" selected>Umm Al-Qura University, Makkah</option>
    			<option value="5">Egyptian General Authority of Survey</option>
    			<option value="7">Institute of Geophysics, University of Tehran</option>
    			<option value="8">Gulf Region</option>
    			<option value="9">Kuwait</option>
    			<option value="10">Qatar</option>
    			<option value="11">Majlis Ugama Islam Singapura, Singapore</option>
    			<option value="12">Union Organization islamic de France</option>
    			<option value="13">Diyanet İşleri Başkanlığı, Turkey</option>
    			<option value="14">Spiritual Administration of Muslims of Russia</option>
    			<option value="15">Moonsighting Committee Worldwide</option>
    			<option value="16">Dubai (experimental)</option>
    			<option value="17">Jabatan Kemajuan Islam Malaysia (JAKIM)</option>
    			<option value="18">Tunisia</option>
    			<option value="19">Algeria</option>
    			<option value="20">KEMENAG - Indonesia</option>
    			<option value="21">Morocco</option>
    			<option value="22">Comunidade Islamica de Lisboa</option>
    			<option value="23">Ministry of Awqaf, Jordan</option>
		   </select>
                </div>
                
                <div class="btn-group">
                    <button class="btn btn-secondary" id="getLocationBtn" aria-label="Use my current location">
                        <i class="fas fa-location-arrow"></i> My Location
                    </button>
                    <button class="btn btn-primary" id="calculateBtn" aria-label="Calculate prayer times">
                        <i class="fas fa-mosque"></i> Get Times
                    </button>
                </div>
            </div>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            <i class="fas fa-spinner fa-spin"></i> Calculating prayer times...
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
        
        <div id="results" style="display: none;">
            <div class="date-display">
                <div class="date-item">
                    <div class="date-label">Gregorian Date</div>
                    <div class="date-value" id="gregorian-date">30 May 2025</div>
                </div>
                <div class="date-item">
                    <div class="date-label">Hijri Date</div>
                    <div class="date-value hijri-date" id="hijri-date">3 Dhul Hijjah 1446 AH</div>
                </div>
                <div class="date-item">
                    <div class="date-label">Location</div>
                    <div class="date-value" id="location-display">Mecca, Saudi Arabia</div>
                </div>
            </div>
            
            <div class="card">
                <h2 style="margin-bottom: 20px; text-align: center; color: #a8edea;">
                    <i class="fas fa-clock"></i> Today's Prayer Times
                </h2>
                
                <div class="prayer-grid" id="prayer-times-grid">
                    <!-- Prayer times will be inserted here -->
                </div>
            </div>
            
            <div class="card">
                <div class="chart-header">
                    <h2 class="chart-title">
                        <i class="fas fa-chart-line"></i> Monthly Prayer Times Analysis
                    </h2>
                    <div class="chart-toggle">
                        <button class="toggle-btn active" id="btn-prayer-times" aria-label="Show prayer times chart">Prayer Times</button>
                        <button class="toggle-btn" id="btn-temporal-hours" aria-label="Show temporal hours chart">Temporal Hours</button>
                        <button class="toggle-btn" id="btn-combined" aria-label="Show combined chart">Combined View</button>
                    </div>
                </div>
                <div style="text-align: center; margin-bottom: 15px; font-size: 0.9rem; color: rgba(255,255,255,0.7);">
                    <i class="fas fa-info-circle"></i> Displays prayer times and temporal hours trends over a 120-day period to track seasonal changes.
                </div>
                
                <div class="chart-container">
                    <canvas id="prayerChart"></canvas>
                    <div id="chart-loading" class="chart-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i> Loading monthly prayer data...
                    </div>
                </div>
            </div>
        </div>
    </div>    <script>
const HIJRI_MONTHS = ["Muḥarram", "Ṣafar", "Rabīʿ al-Awwal", "Rabīʿ al-Thānī", 
                     "Jumādá al-Ūlá", "Jumādá al-Ākhirah", "Rajab", "Shaʿbān", 
                     "Ramaḍān", "Shawwāl", "Dhū al-Qaʿdah", "Dhū al-Ḥijjah"];

const PRAYER_CONFIG = {
  names: {
    Fajr: { iconClass: 'icon-fajr', color: '#4A90E2' },
    Sunrise: { iconClass: 'icon-sunrise', color: '#F5A623' },
    Dhuhr: { iconClass: 'icon-dhuhr', color: '#FF6B6B' },
    Asr: { iconClass: 'icon-asr', color: '#50E3C2' },
    Maghrib: { iconClass: 'icon-maghrib', color: '#B8E986' },
    Isha: { iconClass: 'icon-isha', color: '#BD10E0' }
  },
  order: ['Fajr', 'Sunrise', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'],
  methods: [
    {id: 0, name: "Shia Ithna-Ashari"}, {id: 1, name: "Karachi"}, {id: 2, name: "North America"},
    {id: 3, name: "Muslim World League"}, {id: 4, name: "Umm Al-Qura"}, {id: 5, name: "Egyptian"},
    {id: 7, name: "Tehran"}, {id: 8, name: "Gulf Region"}, {id: 9, name: "Kuwait"},
    {id: 10, name: "Qatar"}, {id: 11, name: "Singapore"}, {id: 12, name: "France"},
    {id: 13, name: "Turkey"}, {id: 14, name: "Russia"}, {id: 15, name: "Moonsighting"},
    {id: 16, name: "Dubai"}, {id: 17, name: "JAKIM"}, {id: 18, name: "Tunisia"},
    {id: 19, name: "Algeria"}, {id: 20, name: "Indonesia"}, {id: 21, name: "Morocco"},
    {id: 22, name: "Lisbon"}, {id: 23, name: "Jordan"}
  ]
};

const TEMPORAL_CONFIG = {
  day: { name: 'Day Hour', iconClass: 'icon-day-hour', color: '#F5A623' },
  night: { name: 'Night Hour', iconClass: 'icon-night-hour', color: '#4A90E2' },
  midNight: { name: 'Midnight', iconClass: 'icon-midnight', color: '#9B59B6' },
  lastThird: { name: 'Last Third', iconClass: 'icon-last-third', color: '#3498DB' }
};

const LEAP_YEAR_CYCLE = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
const ISLAMIC_EPOCH_OPTIONS = {
  ASTRONOMICAL: new Date(Date.UTC(622, 6, 15, 0, 0, 0)),
  TRADITIONAL: new Date(Date.UTC(622, 6, 16, 0, 0, 0)),
  CURRENT: new Date(Date.UTC(622, 6, 19, 0, 0, 0)),
  SAUDI: new Date(Date.UTC(622, 6, 14, 0, 0, 0))
};
const ISLAMIC_EPOCH = ISLAMIC_EPOCH_OPTIONS.ASTRONOMICAL;
const ISLAMIC_EPOCH_DAYS = Math.floor(ISLAMIC_EPOCH.getTime() / 86400000);
const CACHE_EXPIRATION_MS = 3600000;
const MAX_CACHE_SIZE = 1000;
const REQUEST_TIMEOUT_MS = 10000;
const PRECISE_AVERAGE_HIJRI_YEAR_DAYS = 354.36666666666667;

const API_CACHE = {
  store: new Map(),
  requestPromises: new Map(),
  
  get(key) {
    const item = this.store.get(key);
    if (!item) return null;
    if (Date.now() - item.timestamp > CACHE_EXPIRATION_MS) {
      this.store.delete(key);
      return null;
    }
    return item.data;
  },
  
  set(key, data) {
    if (this.store.size >= MAX_CACHE_SIZE) {
      const oldestKey = this.store.keys().next().value;
      this.store.delete(oldestKey);
    }
    this.store.set(key, { timestamp: Date.now(), data });
  },

  getOrSetPromise(key, promiseFactory) {
    if (this.requestPromises.has(key)) {
      return this.requestPromises.get(key);
    }
    
    const promise = promiseFactory().finally(() => {
      this.requestPromises.delete(key);
    });
    
    this.requestPromises.set(key, promise);
    return promise;
  }
};

const STATE = {
  location: { lat: null, lng: null },
  locationName: null,
  prayerTimes: {},
  prayerStats: {},
  monthlyData: {},
  nextPrayer: null,
  hijriDate: null,
  locationTimeZone: null,
  userTimeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  lastGeocodeRequest: 0,
  lastHijriUpdate: 0,
  chartType: 'prayer',
  chart: null,
  updateTimers: { gregorian: null, hijri: null }
};

const DOM_ELEMENTS = {
  loading: document.getElementById('loading'),
  error: document.getElementById('error'),
  results: document.getElementById('results'),
  prayerGrid: document.getElementById('prayer-times-grid'),
  gregorianDate: document.getElementById('gregorian-date'),
  hijriDate: document.getElementById('hijri-date'),
  locationDisplay: document.getElementById('location-display'),
  chartCanvas: document.getElementById('prayerChart'),
  chartLoading: document.getElementById('chart-loading'),
  getLocationBtn: document.getElementById('getLocationBtn')
};

const apiEndpoints = [
  dateKey => {
    const [year, month, day] = dateKey.split('-');
    return `https://api.aladhan.com/v1/gToH?date=${day}-${month}-${year}`;
  },
  dateKey => {
    const [year, month] = dateKey.split('-');
    return `https://api.aladhan.com/v1/gToHCalendar/${month}/${year}`;
  }
];

function getDateInTimezone(timezone = null, specificDate = null) {
  const targetDate = specificDate || new Date();
  
  if (!timezone) {
    return {
      year: targetDate.getFullYear(),
      month: targetDate.getMonth() + 1,
      day: targetDate.getDate(),
      hour: targetDate.getHours(),
      minute: targetDate.getMinutes(),
      second: targetDate.getSeconds(),
      totalSeconds: targetDate.getHours() * 3600 + targetDate.getMinutes() * 60 + targetDate.getSeconds()
    };
  }

  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: timezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });

  const parts = formatter.formatToParts(targetDate);
  const components = Object.fromEntries(parts.map(part => [part.type, part.value]));
  
  const hour = parseInt(components.hour);
  const minute = parseInt(components.minute);
  const second = parseInt(components.second);
  
  return {
    year: parseInt(components.year),
    month: parseInt(components.month),
    day: parseInt(components.day),
    hour,
    minute,
    second,
    totalSeconds: hour * 3600 + minute * 60 + second
  };
}

function parseTimeString(timeString) {
  if (!timeString || typeof timeString !== 'string' || timeString === '--:--') {
    return null;
  }
  
  const cleanTime = timeString.replace(/\s*(AM|PM)\s*/i, '').trim();
  const timeParts = cleanTime.split(':');
  
  if (timeParts.length < 2) return null;
  
  const hours = parseInt(timeParts[0], 10);
  const minutes = parseInt(timeParts[1], 10);
  const seconds = timeParts.length > 2 ? parseInt(timeParts[2], 10) : 0;
  
  if (!Number.isInteger(hours) || !Number.isInteger(minutes) || 
      hours < 0 || hours > 23 || minutes < 0 || minutes > 59 ||
      seconds < 0 || seconds > 59) {
    return null;
  }

  let adjustedHours = hours;
  const isPM = timeString.toLowerCase().includes('pm');
  const isAM = timeString.toLowerCase().includes('am');
  
  if (isPM && hours !== 12) adjustedHours += 12;
  if (isAM && hours === 12) adjustedHours = 0;

  return {
    hours: adjustedHours,
    minutes,
    seconds,
    totalSeconds: adjustedHours * 3600 + minutes * 60 + seconds,
    totalMinutes: adjustedHours * 60 + minutes + seconds / 60
  };
}



function isHijriLeapYear(year) {
  return LEAP_YEAR_CYCLE.includes(year % 30);
}

function getHijriYearLength(year) {
  return isHijriLeapYear(year) ? 355 : 354;
}

function getHijriMonthLengths(year) {
  return isHijriLeapYear(year) 
    ? [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 30] 
    : [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];
}

function calculateDaysSinceIslamicEpoch(gregorianDate) {
  const utcMidnight = new Date(Date.UTC(
    gregorianDate.getFullYear(),
    gregorianDate.getMonth(),
    gregorianDate.getDate(),
    0, 0, 0
  ));
  return Math.floor(utcMidnight.getTime() / 86400000) - ISLAMIC_EPOCH_DAYS;
}





function formatDisplayTime(timeString) {
  if (!isValidTimeFormat(timeString)) {
    return "Invalid Time";
  }

  try {
    const minutes = timeToMinutes(timeString);
    if (isNaN(minutes)) return "Invalid Time";

    return formatMinutesAsDisplayTime(minutes);
  } catch (error) {
    console.error('Error in formatDisplayTime:', error, 'Input:', timeString);
    return "Error";
  }
}



function convertDateToDDMMYYYY(dateString) {
  const [year, month, day] = dateString.split('-');
  return `${day}-${month}-${year}`;
}

function createHijriDateObject(day, month, year, weekday) {
  return {
    day,
    month: { number: month, name: HIJRI_MONTHS[month - 1] },
    year,
    weekday: typeof weekday === 'string' ? { en: weekday } : weekday
  };
}

function computeHijriFromGregorian(gregorianDate) {
  const daysSinceEpoch = calculateDaysSinceIslamicEpoch(gregorianDate);
  
  if (daysSinceEpoch < 0) {
    return createHijriDateObject(1, 1, 1, gregorianDate.toLocaleDateString('en-US', { weekday: 'long' }));
  }

  const { year, remainingDaysInYear } = calculateHijriYear(daysSinceEpoch);
  const { month, day } = calculateHijriMonthAndDay(year, remainingDaysInYear);
  const weekday = gregorianDate.toLocaleDateString('en-US', { weekday: 'long' });

  return createHijriDateObject(day, month, year, weekday);
}

function calculateHijriYear(daysSinceEpoch) {
  const estimatedYear = Math.floor(daysSinceEpoch / PRECISE_AVERAGE_HIJRI_YEAR_DAYS) + 1;
  
  let year = Math.max(1, estimatedYear - 2);
  let totalDaysProcessed = 0;
  
  for (let y = 1; y < year; y++) {
    totalDaysProcessed += getHijriYearLength(y);
  }
  
  while (totalDaysProcessed + getHijriYearLength(year) <= daysSinceEpoch) {
    totalDaysProcessed += getHijriYearLength(year);
    year++;
  }

  return { year, remainingDaysInYear: daysSinceEpoch - totalDaysProcessed };
}

function calculateHijriMonthAndDay(year, remainingDaysInYear) {
  const monthLengths = getHijriMonthLengths(year);
  let month = 1;
  let daysLeft = remainingDaysInYear;
  
  for (let i = 0; i < monthLengths.length; i++) {
    if (daysLeft < monthLengths[i]) {
      month = i + 1;
      break;
    }
    daysLeft -= monthLengths[i];
  }
  
  if (month > 12) {
    console.warn(`Hijri calculation error: month ${month} > 12 for year ${year}, days ${remainingDaysInYear}`);
    month = 12;
    daysLeft = monthLengths[11] - 1;
  }
  
  const day = Math.max(1, Math.min(daysLeft + 1, monthLengths[month - 1]));
  
  return { month, day };
}

function scheduleUpdate(timezone, targetHour, targetMinute, callback) {
  return scheduleUpdatePrecise(timezone, targetHour, targetMinute, 0, callback);
}

function scheduleUpdatePrecise(timezone, targetHour, targetMinute, targetSecond, callback) {
  const now = new Date();

  // Get current time in target timezone
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: timezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });

  const parts = formatter.formatToParts(now);
  const partMap = {};
  parts.forEach(part => {
    if (part.type !== 'literal') {
      partMap[part.type] = parseInt(part.value, 10);
    }
  });

  const currentTotalSeconds = partMap.hour * 3600 + partMap.minute * 60 + partMap.second;
  const targetTotalSeconds = targetHour * 3600 + targetMinute * 60 + targetSecond;

  // Calculate delay in seconds
  let delaySeconds;
  if (currentTotalSeconds < targetTotalSeconds) {
    delaySeconds = targetTotalSeconds - currentTotalSeconds;
  } else {
    delaySeconds = (86400 - currentTotalSeconds) + targetTotalSeconds;
  }

  const delayMs = Math.max(delaySeconds * 1000, 1000);

  return setTimeout(callback, delayMs);
}

function getTimezoneOffsetMs(timezone, date) {
  const utcDate = new Date(date.getTime() + (date.getTimezoneOffset() * 60000));
  const targetDateStr = utcDate.toLocaleString('en-CA', { timeZone: timezone });
  const targetDate = new Date(targetDateStr);
  return targetDate.getTime() - utcDate.getTime();
}

function convertToUTC(localDate, timezone) {
  if (!timezone) return localDate;
  
  try {
    const formatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
    
    const parts = formatter.formatToParts(localDate);
    const dateComponents = {};
    parts.forEach(part => {
      if (part.type !== 'literal') {
        dateComponents[part.type] = parseInt(part.value, 10);
      }
    });
    
    const targetDate = new Date(
      dateComponents.year,
      dateComponents.month - 1,
      dateComponents.day,
      dateComponents.hour,
      dateComponents.minute,
      dateComponents.second
    );
    
    const localOffset = localDate.getTimezoneOffset() * 60000;
    const targetOffset = getTimezoneOffsetMs(timezone, localDate);
    
    return new Date(localDate.getTime() + localOffset - targetOffset);
  } catch (error) {
    console.error('Timezone conversion failed:', error);
    return localDate;
  }
}

function setIslamicEpoch(epochType = 'ASTRONOMICAL') {
  if (ISLAMIC_EPOCH_OPTIONS[epochType]) {
    return {
      epoch: ISLAMIC_EPOCH_OPTIONS[epochType],
      epochDays: Math.floor(ISLAMIC_EPOCH_OPTIONS[epochType].getTime() / 86400000)
    };
  }
  throw new Error(`Invalid epoch type: ${epochType}`);
}

function calculateDynamicThresholds(prayerTimes, latitude = null) {
  const fajrTime = parseTimeString(prayerTimes.Fajr);
  const maghribTime = parseTimeString(prayerTimes.Maghrib);
  
  const dayLength = maghribTime && fajrTime ?
    maghribTime.totalMinutes - fajrTime.totalMinutes : 720;
  const adaptiveBuffer = Math.max(60, Math.min(120, dayLength * 0.1));
  
  let morningThreshold = fajrTime ? Math.max(fajrTime.totalMinutes - adaptiveBuffer, 240) : 360;
  let eveningThreshold = maghribTime ? Math.min(maghribTime.totalMinutes + adaptiveBuffer, 1320) : 1080;
  
  if (latitude !== null) {
    const absLatitude = Math.abs(latitude);
    if (absLatitude > 45) {
      const latitudeAdjustment = (absLatitude - 45) * 2;
      morningThreshold = Math.max(morningThreshold, 270 + latitudeAdjustment);
      eveningThreshold = Math.min(eveningThreshold, 1230 - latitudeAdjustment);
    }
  }
  
  return { morningThreshold, eveningThreshold };
}

function isPrayerTimePassed(prayerTime, prayerName = null) {
  const parsedTime = parseTimeString(prayerTime);
  if (!parsedTime) return false;

  const currentTime = getCurrentTimeInLocation();
  const currentTotalSeconds = currentTime.getHours() * 3600 + 
                             currentTime.getMinutes() * 60 + 
                             currentTime.getSeconds();
  
  let prayerDateTime = new Date(currentTime);
  prayerDateTime.setHours(parsedTime.hours, parsedTime.minutes, parsedTime.seconds, 0);
  
  if (prayerName && STATE.prayerTimes) {
    const thresholds = calculateDynamicThresholds(STATE.prayerTimes, STATE.location.lat);
    const isNightPrayer = ['Maghrib', 'Isha'].includes(prayerName);
    const isEarlyMorningPrayer = ['Fajr'].includes(prayerName);
    
    if (currentTotalSeconds < thresholds.morningThreshold * 60 && isNightPrayer) {
      prayerDateTime.setDate(prayerDateTime.getDate() - 1);
    } else if (currentTotalSeconds > thresholds.eveningThreshold * 60 && isEarlyMorningPrayer) {
      prayerDateTime.setDate(prayerDateTime.getDate() + 1);
    }
  }
  
  return currentTime.getTime() > prayerDateTime.getTime();
}

function getCurrentTimeInLocation() {
  const now = new Date();
  const timezone = STATE.locationTimeZone || STATE.userTimeZone;
  
  if (!timezone) return now;
  
  try {
    const formatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
    
    const parts = formatter.formatToParts(now);
    const components = {};
    parts.forEach(part => {
      if (part.type !== 'literal') {
        components[part.type] = parseInt(part.value, 10);
      }
    });
    
    return new Date(
      components.year,
      components.month - 1,
      components.day,
      components.hour,
      components.minute,
      components.second
    );
  } catch (error) {
    console.error('Timezone conversion failed:', error);
    return now;
  }
}

function createPrayerDateTime(baseDate, parsedTime) {
  const prayerDateTime = new Date(baseDate);
  prayerDateTime.setHours(parsedTime.hours, parsedTime.minutes, parsedTime.seconds, 0);
  return prayerDateTime;
}

function convertToTargetTimeZone(date, targetTimeZone) {
  if (!targetTimeZone) return date;
  
  try {
    return createDateFromTimeZone(date, targetTimeZone);
  } catch (error) {
    console.error('Time zone conversion failed:', error);
    return date;
  }
}

function createDateFromTimeZone(date, timeZone) {
  const formatter = new Intl.DateTimeFormat('en-US', {
    timeZone,
    year: 'numeric', month: 'numeric', day: 'numeric',
    hour: 'numeric', minute: 'numeric', second: 'numeric'
  });
  
  const parts = formatter.formatToParts(date);
  const partMap = createDatePartsMap(parts);
  
  return new Date(Date.UTC(
    parseInt(partMap.year, 10), parseInt(partMap.month, 10) - 1, parseInt(partMap.day, 10),
    parseInt(partMap.hour, 10), parseInt(partMap.minute, 10), parseInt(partMap.second, 10)
  ));
}

function createDatePartsMap(parts) {
  const partMap = {};
  parts.forEach(part => {
    if (part.type !== 'literal') {
      partMap[part.type] = part.value;
    }
  });
  return partMap;
}

function formatDateWithWeekday(date) {
  const targetTimeZone = STATE.locationTimeZone || STATE.userTimeZone;
  const options = { 
    weekday: 'long', month: 'long', day: 'numeric', year: 'numeric',
    timeZone: targetTimeZone
  };
  return date.toLocaleDateString('en-US', options);
}

function formatDateAsYMD(date, timeZone) {
  const options = { year: 'numeric', month: '2-digit', day: '2-digit', timeZone };
  return new Intl.DateTimeFormat('sv-SE', options).format(date);
}

function formatHijriDisplay(hijriDate, gregorianDate) {
  if (!hijriDate || !hijriDate.month || !hijriDate.day || !hijriDate.year) {
    return "Invalid Hijri date";
  }
  
  const displayDate = gregorianDate || getCurrentAdjustedDate();
  const weekday = displayDate.toLocaleDateString('en-US', { 
    weekday: 'long', 
    timeZone: STATE.locationTimeZone || STATE.userTimeZone
  });
  
  const weekdayText = hijriDate.weekday?.en || weekday;
  const monthIndex = hijriDate.month.number - 1;
  const monthName = (monthIndex >= 0 && monthIndex < HIJRI_MONTHS.length) 
    ? HIJRI_MONTHS[monthIndex] 
    : hijriDate.month.name || `Month ${hijriDate.month.number}`;
  
  return `${weekdayText}, ${hijriDate.day} ${monthName} ${hijriDate.year} AH`;
}

function formatGregorianDateDisplay() {
  const timezone = STATE.locationTimeZone || STATE.userTimeZone;
  const options = {
    weekday: 'long',
    month: 'long',
    day: 'numeric',      
    year: 'numeric',
    timeZone: timezone
  };
  
  return new Date().toLocaleDateString('en-US', options);
}

function getCurrentAdjustedDateTime(timeZone) {
  const now = new Date();
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: timeZone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });

  const parts = formatter.formatToParts(now);
  const partMap = {};
  parts.forEach(part => {
    if (part.type !== 'literal') {
      partMap[part.type] = part.value;
    }
  });

  return new Date(
    `${partMap.year}-${partMap.month}-${partMap.day}T${partMap.hour}:${partMap.minute}:${partMap.second}.${now.getMilliseconds().toString().padStart(3, '0')}`
  );
}

function getCurrentHijriAdjustedDateTime(timeZone) {
  const now = new Date();
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: timeZone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });

  const parts = formatter.formatToParts(now);
  const partMap = {};
  parts.forEach(part => {
    if (part.type !== 'literal') {
      partMap[part.type] = part.value;
    }
  });

  // Create date in UTC to avoid timezone issues
  const currentDate = new Date(
    `${partMap.year}-${partMap.month}-${partMap.day}T${partMap.hour}:${partMap.minute}:${partMap.second}.000Z`
  );

  // For Hijri dates, check if we're past Maghrib time
  if (STATE.prayerTimes?.Maghrib) {
    const maghribTime = parseTimeString(STATE.prayerTimes.Maghrib);
    if (maghribTime) {
      const currentTotalSeconds = parseInt(partMap.hour) * 3600 + parseInt(partMap.minute) * 60 + parseInt(partMap.second);
      const maghribTotalSeconds = maghribTime.totalSeconds;



      // If current time is past Maghrib, the Hijri date should be tomorrow's Gregorian date
      if (currentTotalSeconds >= maghribTotalSeconds) {
        // Calculate next day in the target timezone
        const nextDayYear = parseInt(partMap.year);
        const nextDayMonth = parseInt(partMap.month);
        const nextDayDate = parseInt(partMap.day);

        // Create next day date properly in UTC
        const nextDay = new Date(`${nextDayYear}-${nextDayMonth.toString().padStart(2, '0')}-${(nextDayDate + 1).toString().padStart(2, '0')}T00:00:00.000Z`);

        // Handle month/year rollover
        if (nextDay.getUTCDate() !== nextDayDate + 1) {
          // Month rollover occurred, recalculate
          const tempDate = new Date(Date.UTC(nextDayYear, nextDayMonth - 1, nextDayDate));
          tempDate.setUTCDate(tempDate.getUTCDate() + 1);
          const adjustedNextDay = new Date(`${tempDate.getUTCFullYear()}-${(tempDate.getUTCMonth() + 1).toString().padStart(2, '0')}-${tempDate.getUTCDate().toString().padStart(2, '0')}T00:00:00.000Z`);
          return adjustedNextDay;
        }

        return nextDay;
      }
    }
  }

  return currentDate;
}

function buildApiUrlWithTimezone(endpointFunction, dateKey) {
  const baseUrl = endpointFunction(dateKey);
  const targetTimeZone = STATE.locationTimeZone || STATE.userTimeZone;
  const urlQuerySeparator = baseUrl.includes('?') ? '&' : '?';
  return `${baseUrl}${urlQuerySeparator}timezone=${encodeURIComponent(targetTimeZone)}`;
}

function createRequestController() {
  const requestController = new AbortController();
  const requestTimeout = setTimeout(() => requestController.abort(), REQUEST_TIMEOUT_MS);
  
  return { requestController, requestTimeout };
}

function parseHijriApiResponse(apiData, fallbackWeekday) {
  if (apiData.code !== 200 || !apiData.data?.hijri) return null;
  
  const hijriData = apiData.data.hijri;
  return createHijriFromApiData(hijriData, fallbackWeekday);
}

function createHijriFromApiData(hijriData, fallbackWeekday) {
  const monthNumber = safeParseInt(hijriData.month?.number, 1, 1, 12);
  const monthIndex = monthNumber - 1;
  const isValidMonth = monthIndex >= 0 && monthIndex < HIJRI_MONTHS.length;
  
  return {
    day: safeParseInt(hijriData.day, 1, 1, 30),
    month: { 
      number: monthNumber, 
      name: hijriData.month.en || (isValidMonth ? HIJRI_MONTHS[monthIndex] : `Month ${monthNumber}`)
    },
    year: safeParseInt(hijriData.year, 1445, 1000, 3000),
    weekday: hijriData.weekday || { en: fallbackWeekday }
  };
}

function parseCalendarApiResponse(apiData, targetDateKey, fallbackWeekday) {
  if (apiData.code !== 200 || !Array.isArray(apiData.data)) return null;
  
  const formattedTargetDate = convertDateToDDMMYYYY(targetDateKey);
  const matchingEntry = findMatchingCalendarEntry(apiData.data, formattedTargetDate);
  
  if (!matchingEntry?.hijri) return null;
  
  return createHijriFromApiData(matchingEntry.hijri, fallbackWeekday);
}

function findMatchingCalendarEntry(calendarData, targetDate) {
  return calendarData.find(entry => {
    return entry.gregorian && entry.hijri && entry.gregorian.date === targetDate;
  });
}

function getInputValue(elementId) {
  const element = document.getElementById(elementId);
  return element ? element.value.trim() : '';
}

function hasValidGeolocation() {
  return STATE.location.lat !== null && STATE.location.lng !== null;
}

function buildPrayerApiUrl() {
  const selectedMethod = getInputValue('method');
  const baseUrl = 'https://api.aladhan.com/v1/';
  
  if (hasValidGeolocation()) {
    return `${baseUrl}timings?latitude=${STATE.location.lat}&longitude=${STATE.location.lng}&method=${selectedMethod}`;
  }
  
  const cityName = encodeURIComponent(getInputValue('city'));
  const countryName = encodeURIComponent(getInputValue('country'));
  return `${baseUrl}timingsByCity?city=${cityName}&country=${countryName}&method=${selectedMethod}`;
}



async function fetchWithRetry(url, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url);
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      return await response.json();
    } catch (error) {
      if (i === retries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 100 * (i + 1)));
    }
  }
}

async function fetchWithCaching(url, cacheKey) {
  const cachedData = API_CACHE.get(cacheKey);
  if (cachedData) return cachedData;
  
  return API_CACHE.getOrSetPromise(cacheKey, async () => {
    const fetchedData = await attemptApiRequest(url);
    API_CACHE.set(cacheKey, fetchedData);
    return fetchedData;
  });
}

async function attemptApiRequest(url) {
  try {
    return await makeDirectApiRequest(url);
  } catch (directError) {
    console.warn('Direct request failed, trying alternative:', directError.message);
    return await makeAlternativeApiRequest(url);
  }
}

async function makeDirectApiRequest(url) {
  const { requestController, requestTimeout } = createRequestController();
  
  try {
    const response = await fetch(url, {
      signal: requestController.signal,
      headers: { 'Accept': 'application/json' }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } finally {
    clearTimeout(requestTimeout);
  }
}

async function makeAlternativeApiRequest(url) {
  const alternativeUrl = url.includes('timingsByCity') 
    ? url.replace('timingsByCity', 'timingsByAddress')
    : url;
  
  const response = await fetch(alternativeUrl, {
    headers: { 'Accept': 'application/json' }
  });
  
  if (!response.ok) {
    throw new Error(`Alternative request failed: HTTP ${response.status}`);
  }

  return await response.json();
}

async function attemptHijriApiEndpoints(dateKey, fallbackWeekday) {
  for (const endpoint of apiEndpoints) {
    try {
      const apiResult = await tryHijriEndpoint(endpoint, dateKey, fallbackWeekday);
      if (apiResult) return apiResult;
    } catch (error) {
      console.warn('Endpoint failed:', error.message);
    }
  }
  return null;
}

async function tryHijriEndpoint(endpointFunction, dateKey, fallbackWeekday) {
  const url = buildApiUrlWithTimezone(endpointFunction, dateKey);
  const apiData = await makeDirectApiRequest(url);
  
  if (url.includes('gToHCalendar')) {
    return parseCalendarApiResponse(apiData, dateKey, fallbackWeekday);
  }
  return parseHijriApiResponse(apiData, fallbackWeekday);
}

async function updateHijriDateState(gregorianDate) {
  const targetTimeZone = STATE.locationTimeZone || STATE.userTimeZone;
  const dateKey = formatDateAsYMD(gregorianDate, targetTimeZone);
  const cacheKey = `hijri_${dateKey}_${targetTimeZone}`;
  
  const cachedHijri = API_CACHE.get(cacheKey);
  if (cachedHijri) return cachedHijri;
  
  const fallbackWeekday = gregorianDate.toLocaleDateString('en-US', {
    weekday: 'long',
    timeZone: targetTimeZone
  });
  
  try {
    const apiResult = await attemptHijriApiEndpoints(dateKey, fallbackWeekday);
    if (apiResult) {
      API_CACHE.set(cacheKey, apiResult);
      return apiResult;
    }
  } catch (error) {
    console.warn('API fetch failed, using calculation:', error);
  }
  
  const calculated = computeHijriFromGregorian(gregorianDate);
  API_CACHE.set(cacheKey, calculated);
  return calculated;
}

function clearTimers() {
  Object.values(STATE.updateTimers).forEach(timer => clearTimeout(timer));
  STATE.updateTimers = { gregorian: null, hijri: null };
}

function showLoadingState() {
  DOM_ELEMENTS.error.style.display = 'none';
  DOM_ELEMENTS.results.style.display = 'none';
  DOM_ELEMENTS.loading.style.display = 'block';
}

function showResultsView() {
  DOM_ELEMENTS.loading.style.display = 'none';
  DOM_ELEMENTS.error.style.display = 'none';
  DOM_ELEMENTS.results.style.display = 'block';
}

function showStatusMessage(message) {
  DOM_ELEMENTS.loading.style.display = 'none';
  DOM_ELEMENTS.results.style.display = 'none';
  DOM_ELEMENTS.error.textContent = message;
  DOM_ELEMENTS.error.style.display = 'block';
}

function clearPrayerTimesDisplay() {
  const container = DOM_ELEMENTS.prayerGrid;
  if (!container) return;
  
  const timeElements = container.querySelectorAll('.prayer-time');
  timeElements.forEach(el => el.textContent = '--:--');
}

function updateGregorianDisplay() {
  clearTimeout(STATE.updateTimers.gregorian);
  
  try {
    const displayDate = formatGregorianDateDisplay();
    const element = DOM_ELEMENTS.gregorianDate;
    if (element) element.textContent = displayDate;
    
    const timezone = STATE.locationTimeZone || STATE.userTimeZone;
    STATE.updateTimers.gregorian = scheduleUpdate(timezone, 0, 0, updateGregorianDisplay);
  } catch (error) {
    console.error('Gregorian date error:', error.message);
    const element = DOM_ELEMENTS.gregorianDate;
    if (element) element.textContent = "Error loading date";
    
    STATE.updateTimers.gregorian = setTimeout(updateGregorianDisplay, 300000);
  }
}

async function updateHijriDisplay() {
  clearTimeout(STATE.updateTimers.hijri);

  // Prevent rapid successive calls
  const now = Date.now();
  if (STATE.lastHijriUpdate && (now - STATE.lastHijriUpdate) < 5000) {
    return;
  }
  STATE.lastHijriUpdate = now;

  const timezone = STATE.locationTimeZone || STATE.userTimeZone;
  if (!timezone) {
    const element = DOM_ELEMENTS.hijriDate;
    if (element) element.textContent = "Timezone not available";
    return;
  }
  
  try {
    const currentDate = getCurrentHijriAdjustedDateTime(timezone);
    const hijriData = await updateHijriDateState(currentDate);
    const formattedHijri = formatHijriDisplay(hijriData, currentDate);
    
    const element = DOM_ELEMENTS.hijriDate;
    if (element) {
      const updateTime = new Date().toLocaleTimeString();
      element.textContent = formattedHijri;
      element.title = `Last updated: ${updateTime}`;
    }
    
    STATE.hijriDate = hijriData;
    
    let nextUpdateHour = 0;
    let nextUpdateMinute = 0;

    if (STATE.prayerTimes?.Maghrib) {
      const maghribTime = parseTimeString(STATE.prayerTimes.Maghrib);
      if (maghribTime) {
        nextUpdateHour = maghribTime.hours;
        nextUpdateMinute = maghribTime.minutes;
      }
    }

    STATE.updateTimers.hijri = scheduleUpdate(
      timezone,
      nextUpdateHour,
      nextUpdateMinute,
      updateHijriDisplay
    );
  } catch (error) {
    console.error('Hijri date error:', error.message);
    const element = DOM_ELEMENTS.hijriDate;
    if (element) element.textContent = "Error loading Hijri date";
    
    STATE.updateTimers.hijri = setTimeout(updateHijriDisplay, 300000);
  }
}

async function updateDateDisplays() {
  updateGregorianDisplay();
  await updateHijriDisplay();
}

async function fetchPrayerData() {
  const calculateBtn = document.getElementById('calculateBtn');
  if (calculateBtn.disabled) return;
  
  calculateBtn.disabled = true;
  calculateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
  
  try {
    showLoadingState();
    STATE.hijriDate = null;
    STATE.prayerTimes = {};
    
    if (STATE.chart) {
      STATE.chart.destroy();
      STATE.chart = null;
    }
    
    DOM_ELEMENTS.chartLoading.style.display = 'none';
    DOM_ELEMENTS.hijriDate.textContent = "Loading...";
    
    const city = getInputValue('city');
    const country = getInputValue('country');
    
    if (!city.trim() || !country.trim()) {
      showStatusMessage('Please enter both city and country');
      return;
    }

    if (city.length > 100 || country.length > 100) {
      showStatusMessage('Location name too long');
      return;
    }

    // Basic validation for potentially malicious input
    const invalidChars = /[<>\"'&]/;
    if (invalidChars.test(city) || invalidChars.test(country)) {
      showStatusMessage('Invalid characters in location name');
      return;
    }
    
    if (city && country) {
      STATE.location = { lat: null, lng: null };
      STATE.locationName = `${city}, ${country}`;
    }
    const apiUrl = buildPrayerApiUrl();
    const responseData = await fetchWithCaching(apiUrl, `prayer_${apiUrl}`);
    const prayerData = processApiResponse(responseData);
    
    STATE.locationTimeZone = prayerData.meta?.timezone || STATE.userTimeZone;
    STATE.prayerTimes = {...prayerData.timings};
    calculateDuhaPrayerWindow();
    
    await computeComparativeStatistics();
    renderPrayerInterface();
    
    DOM_ELEMENTS.locationDisplay.textContent = STATE.locationName || 
      `${city || 'Unknown'}, ${country || 'Unknown'}`;
    
    await updateDateDisplays();
    showResultsView();
    
    try {
      STATE.monthlyData = {};
      clearMonthlyDataCache();
      await fetchMonthlyData();
      
      if (Object.keys(STATE.monthlyData).length > 0) {
        setTimeout(() => {
          renderChart('prayer');
        }, 100);
      } else {
        console.warn('No monthly data available for chart rendering');
        DOM_ELEMENTS.chartLoading.style.display = 'none';
      }
    } catch (error) {
      console.error('Failed to load chart data:', error);
      DOM_ELEMENTS.chartLoading.style.display = 'none';
    }
  } catch (error) {
    console.error('Prayer data fetch error:', error);
    showStatusMessage(`Error: ${error.message || 'Failed to load data'}`);
  } finally {
    calculateBtn.disabled = false;
    calculateBtn.innerHTML = '<i class="fas fa-mosque"></i> Get Times';
  }
}

function calculateDuhaPrayerWindow() {
  if (!STATE.prayerTimes.Sunrise || !STATE.prayerTimes.Dhuhr) return;
  
  const sunriseSeconds = timeToSeconds(STATE.prayerTimes.Sunrise);
  const dhuhrSeconds = timeToSeconds(STATE.prayerTimes.Dhuhr);
  
  if (sunriseSeconds >= dhuhrSeconds) return;
  
  const updatedTimes = {...STATE.prayerTimes};
  updatedTimes.DuhaStart = secondsToTime(sunriseSeconds + (20 * 60));
  updatedTimes.DuhaMid = secondsToTime(sunriseSeconds + Math.round((dhuhrSeconds - sunriseSeconds) / 2));
  updatedTimes.DuhaEnd = secondsToTime(dhuhrSeconds - (10 * 60));
  
  STATE.prayerTimes = updatedTimes;
}

async function fetchMethodData(methodId) {
  try {
    const baseUrl = buildPrayerApiUrl();
    const methodSpecificUrl = baseUrl.replace(/method=\d+/, `method=${methodId}`);
    const cacheKey = `method_${methodId}_${btoa(methodSpecificUrl).slice(-12)}`;
    
    const cached = API_CACHE.get(cacheKey);
    if (cached) {
      return cached;
    }
    
    const responseData = await fetchWithRetry(methodSpecificUrl, 2);
    const data = processApiResponse(responseData);
    
    API_CACHE.set(cacheKey, data);
    return data;
  } catch (error) {
    console.error(`Failed to fetch data for method ${methodId}:`, error);
    throw error;
  }
}

function calculateCircularMean(minuteValues) {
  const radians = minuteValues.map(min => (min / 1440) * 2 * Math.PI);
  const sinSum = radians.reduce((sum, rad) => sum + Math.sin(rad), 0);
  const cosSum = radians.reduce((sum, rad) => sum + Math.cos(rad), 0);
  
  const meanRad = Math.atan2(sinSum / minuteValues.length, cosSum / minuteValues.length);
  let meanMinutes = (meanRad / (2 * Math.PI)) * 1440;
  if (meanMinutes < 0) meanMinutes += 1440;
  
  return Math.round(meanMinutes);
}

function calculateMedianValue(sortedValues) {
  const midIndex = Math.floor(sortedValues.length / 2);
  return sortedValues.length % 2 !== 0 ? 
    sortedValues[midIndex] : 
    Math.round((sortedValues[midIndex - 1] + sortedValues[midIndex]) / 2);
}

function calculateTimeDistribution(times) {
  const minuteValues = times.map(timeToMinutes);
  const sortedValues = [...minuteValues].sort((a, b) => a - b);
  
  return {
    min: minutesToTime(Math.min(...minuteValues)),
    max: minutesToTime(Math.max(...minuteValues)),
    avg: minutesToTime(calculateCircularMean(minuteValues)),
    median: minutesToTime(calculateMedianValue(sortedValues)),
    count: minuteValues.length
  };
}

async function computeComparativeStatistics() {
  const currentMethod = parseInt(getInputValue('method'), 10);
  const comparisonMethods = PRAYER_CONFIG.methods.filter(m => m.id !== currentMethod);
  
  const prayerResults = {};
  const methodQueue = [...comparisonMethods];
  
  while (methodQueue.length > 0) {
    const batch = methodQueue.splice(0, 3);
    const batchPromises = batch.map(method => 
      fetchMethodData(method.id)
        .then(data => {
          PRAYER_CONFIG.order.forEach(prayer => {
            if (!prayerResults[prayer]) prayerResults[prayer] = [];
            if (data.timings?.[prayer]) prayerResults[prayer].push(data.timings[prayer]);
          });
        })
        .catch(error => console.warn(`Method ${method.id} failed:`, error.message))
    );
    
    await Promise.all(batchPromises);
    if (methodQueue.length > 0) await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  PRAYER_CONFIG.order.forEach(prayer => {
    if (prayerResults[prayer]?.length) {
      STATE.prayerStats[prayer] = calculateTimeDistribution(prayerResults[prayer]);
    }
  });
}



function getCurrentLocationTime() {
  if (!STATE.locationTimeZone) {
    const now = new Date();
    return {
      hour: now.getHours(),
      minute: now.getMinutes(),
      second: now.getSeconds(),
      totalSeconds: now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds()
    };
  }

  try {
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: STATE.locationTimeZone,
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric',
      hour12: false
    });
    
    const parts = formatter.formatToParts(new Date());
    const hour = parts.find(p => p.type === 'hour')?.value || '0';
    const minute = parts.find(p => p.type === 'minute')?.value || '0';
    const second = parts.find(p => p.type === 'second')?.value || '0';
    
    return {
      hour: parseInt(hour, 10),
      minute: parseInt(minute, 10),
      second: parseInt(second, 10),
      totalSeconds: parseInt(hour) * 3600 + parseInt(minute) * 60 + parseInt(second)
    };
  } catch (error) {
    const now = new Date();
    return {
      hour: now.getHours(),
      minute: now.getMinutes(),
      second: now.getSeconds(),
      totalSeconds: now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds()
    };
  }
}

function calculateTimeUntilPrayer(prayerName, currentTime = null) {
  if (!STATE.prayerTimes[prayerName]) return '';
  
  const prayerTime = STATE.prayerTimes[prayerName];
  if (!isValidTimeFormat(prayerTime)) return '';
  
  const current = currentTime || getCurrentLocationTime();
  const prayerSeconds = timeToSeconds(prayerTime);
  let diff = prayerSeconds - current.totalSeconds;
  
  if (diff <= 0) {
    diff += 86400;
  }
  
  if (diff === 0) return 'Now';
  
  const hours = Math.floor(diff / 3600);
  const minutes = Math.floor((diff % 3600) / 60);
  const seconds = diff % 60;
  
  return `in ${hours ? `${hours}h ` : ''}${minutes}m ${seconds}s`;
}

function safeParseInt(value, defaultValue, min, max) {
  return Math.round(safeParseNumber(value, defaultValue, min, max, true));
}

function safeParseNumber(value, defaultValue, min, max, allowFloat = false) {
  const parsed = allowFloat ? parseFloat(value) : parseInt(value, 10);
  if (isNaN(parsed)) return defaultValue;
  return Math.min(Math.max(parsed, min), max);
}

function timeToSeconds(timeStr) {
  if (!timeStr) return 0;

  const parsed = parseTimeString(timeStr);
  if (parsed) {
    return parsed.totalSeconds;
  }

  const [hours, minutes, seconds] = timeStr.split(':').map(Number);
  return (hours || 0) * 3600 + (minutes || 0) * 60 + (seconds || 0);
}

function secondsToTime(totalSeconds) {
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  
  const pad = (num) => num.toString().padStart(2, '0');
  return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
}



function createPrayerCard(prayer, time, isCurrent, timeRemaining) {
  if (!time || !isValidTimeFormat(time)) return '';
  
  const config = PRAYER_CONFIG.names[prayer];
  const stats = STATE.prayerStats[prayer];
  
  return `
    <div class="prayer-card ${isCurrent ? 'current' : ''}">
      <div class="prayer-name">
        <span class="prayer-icon ${config.iconClass}"></span> ${prayer}
      </div>
      <div class="prayer-time">${formatDisplayTime(time)}</div>
      ${timeRemaining ? `<div class="time-until">${timeRemaining}</div>` : ''}
      ${stats?.count >= 3 ? `
        <div class="prayer-stats">
          <div class="stat-item">
            <span class="stat-label">Range:</span>
            <span class="stat-value">${formatDisplayTime(stats.min)} - ${formatDisplayTime(stats.max)}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Average:</span>
            <span class="stat-value">${formatDisplayTime(stats.avg)}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Median:</span>
            <span class="stat-value">${formatDisplayTime(stats.median)}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Compared:</span>
            <span class="stat-value">${stats.count} methods</span>
          </div>
        </div>
      ` : ''}
    </div>
  `;
}

function calculateTemporalPeriods() {
  if (!STATE.prayerTimes.Fajr || !STATE.prayerTimes.Maghrib) return {};
  
  const fajrStr = STATE.prayerTimes.Fajr;
  const maghribStr = STATE.prayerTimes.Maghrib;
  
  if (!isValidTimeFormat(fajrStr)) return {};
  if (!isValidTimeFormat(maghribStr)) return {};
  
  const fajr = timeToMinutes(fajrStr);
  const maghrib = timeToMinutes(maghribStr);
  
  const dayDuration = calculateDaytimeDuration(fajr, maghrib);
  const nightDuration = 1440 - dayDuration;
  
  const dayHourPrecise = dayDuration / 12;
  const nightHourPrecise = nightDuration / 12;
  
  const midNightMinutes = (maghrib + nightDuration / 2) % 1440;
  const lastThirdMinutes = (maghrib + nightDuration * 2 / 3) % 1440;
  
  return {
    day: Math.round(dayHourPrecise * 100) / 100,
    night: Math.round(nightHourPrecise * 100) / 100,
    dayPrecise: dayHourPrecise,
    nightPrecise: nightHourPrecise,
    midNight: minutesToTime(midNightMinutes),
    lastThird: minutesToTime(lastThirdMinutes),
    dayDuration,
    nightDuration
  };
}

function determineNextScheduledPrayer(currentSeconds) {
  if (!STATE.prayerTimes) return '';
  
  const prayers = PRAYER_CONFIG.order.filter(p => p !== 'Sunrise');
  const prayerTimes = prayers
    .map(prayer => ({
      name: prayer,
      time: timeToSeconds(STATE.prayerTimes[prayer])
    }))
    .filter(p => !isNaN(p.time));
  
  const adjustedPrayerTimes = prayerTimes.map(p => {
    if (p.time < currentSeconds) {
      return { ...p, time: p.time + 86400 };
    }
    return p;
  });
  
  const sortedPrayers = [...adjustedPrayerTimes].sort((a, b) => a.time - b.time);
  return sortedPrayers.length > 0 ? sortedPrayers[0].name : '';
}

function renderPrayerInterface() {
  if (!STATE.prayerTimes || Object.keys(STATE.prayerTimes).length === 0) return;
  
  const locationTime = getCurrentLocationTime();
  const nextPrayer = determineNextScheduledPrayer(locationTime.totalSeconds);
  const temporalMetrics = calculateTemporalPeriods();
  
  let cardsHTML = PRAYER_CONFIG.order.map(prayer => {
    const isCurrent = prayer === nextPrayer;
    const timeRemaining = calculateTimeUntilPrayer(prayer, locationTime);
    return createPrayerCard(prayer, STATE.prayerTimes[prayer], isCurrent, timeRemaining);
  }).join('');
  
  cardsHTML += `
    <div class="prayer-card temporal day-temporal">
      <div class="prayer-name">
        <span class="prayer-icon ${TEMPORAL_CONFIG.day.iconClass}"></span> ${TEMPORAL_CONFIG.day.name}
      </div>
      <div class="temporal-time">${temporalMetrics.day.toFixed(2)} min</div>
      <div class="time-until">Per temporal hour</div>
      ${STATE.prayerTimes.DuhaStart && STATE.prayerTimes.DuhaMid && STATE.prayerTimes.DuhaEnd ? `    
        <div class="day-special-times">
          <div class="day-time-item">
            <div class="day-time-label">
              <span class="prayer-icon icon-duha"></span> Duha Prayer Window
            </div>
          </div>
          <div class="duha-window">
            <div class="duha-time">
              <div class="duha-time-label">Begins</div>
              <div class="duha-time-value">${formatDisplayTime(STATE.prayerTimes.DuhaStart)}</div>
            </div>
            <div class="duha-time preferred-time">
              <div class="duha-time-label">Preferred</div>
              <div class="duha-time-value">${formatDisplayTime(STATE.prayerTimes.DuhaMid)}</div>
            </div>
            <div class="duha-time">
              <div class="duha-time-label">Ends</div>
              <div class="duha-time-value">${formatDisplayTime(STATE.prayerTimes.DuhaEnd)}</div>
            </div>
          </div>
        </div>
      ` : ''}
    </div>
    <div class="prayer-card temporal temporal-night">
      <div class="prayer-name">
        <span class="prayer-icon ${TEMPORAL_CONFIG.night.iconClass}"></span> ${TEMPORAL_CONFIG.night.name}
      </div>
      <div class="temporal-time">${temporalMetrics.night.toFixed(2)} min</div>
      <div class="time-until">Per temporal hour</div>
      <div class="night-special-times">
        <div class="night-time-item">
          <div class="night-time-label">
            <span class="prayer-icon ${TEMPORAL_CONFIG.midNight.iconClass}"></span> ${TEMPORAL_CONFIG.midNight.name}
          </div>
          <div class="night-time-value">${temporalMetrics.midNight}</div>
        </div>
        <div class="night-time-item">
          <div class="night-time-label">
            <span class="prayer-icon ${TEMPORAL_CONFIG.lastThird.iconClass}"></span> ${TEMPORAL_CONFIG.lastThird.name}
          </div>
          <div class="night-time-value">${temporalMetrics.lastThird}</div>
        </div>
      </div>
    </div>
  `;
  
  DOM_ELEMENTS.prayerGrid.innerHTML = cardsHTML;
}

function formatDateForAPI(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    throw new Error('Invalid date provided to formatDateForAPI');
  }
  
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, '0');
  const day = String(date.getUTCDate()).padStart(2, '0');
  
  return `${day}-${month}-${year}`;
}

function validateApiTimings(timings) {
  if (!timings || typeof timings !== 'object') return false;
  
  const requiredPrayers = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
  
  return requiredPrayers.every(prayer => {
    const time = timings[prayer];
    return time && isValidTimeFormat(time);
  });
}

function processApiResponse(responseData) {
  if (!responseData?.data?.timings) {
    throw new Error('Invalid API response structure');
  }
  
  const timings = responseData.data.timings;
  
  if (!validateApiTimings(timings)) {
    throw new Error('Invalid prayer times in API response');
  }
  
  return responseData.data;
}

async function fetchDateSpecificData(date) {
  const dateString = formatDateForAPI(date);
  let baseUrl;
  
  if (hasValidGeolocation()) {
    baseUrl = `https://api.aladhan.com/v1/timings/${dateString}?latitude=${STATE.location.lat}&longitude=${STATE.location.lng}&method=${getInputValue('method')}`;
  } else {
    const city = encodeURIComponent(getInputValue('city'));
    const country = encodeURIComponent(getInputValue('country'));
    baseUrl = `https://api.aladhan.com/v1/timingsByCity/${dateString}?city=${city}&country=${country}&method=${getInputValue('method')}`;
  }
  
  const cacheKey = `monthly_${baseUrl}`;
  const responseData = await fetchWithCaching(baseUrl, cacheKey);
  const data = processApiResponse(responseData);
  
  if (!data || !data.timings) {
    throw new Error(`Invalid API response for ${dateString}`);
  }
  
  return data;
}

function clearMonthlyDataCache() {
  if (!API_CACHE?.store) {
    console.warn('API_CACHE.store not available for clearing');
    return;
  }

  try {
    const keysToDelete = [];
    for (const key of API_CACHE.store.keys()) {
      if (typeof key === 'string' &&
          (key.startsWith('monthly_') ||
           key.includes('timings/') ||
           key.includes('timingsByCity/') ||
           key.startsWith('method_'))) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => API_CACHE.store.delete(key));
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
}

function updateActiveChartControl() {
  const buttonIds = {
    'prayer': 'btn-prayer-times',
    'temporal': 'btn-temporal-hours',
    'combined': 'btn-combined'
  };
  
  const activeButtonId = buttonIds[STATE.chartType];
  const activeButton = document.getElementById(activeButtonId);
  if (!activeButton) return;
  
  document.querySelectorAll('.toggle-btn').forEach(btn => 
    btn.classList.remove('active')
  );
  activeButton.classList.add('active');
}

async function fetchMonthlyData() {
  DOM_ELEMENTS.chartLoading.style.display = 'block';
  const newMonthlyData = {};
  
  const today = new Date();
  const startDate = new Date(today);
  startDate.setDate(today.getDate() - 60);
  
  const requestQueue = [];
  for (let i = 0; i < 120; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);
    requestQueue.push(date);
  }
    
  while (requestQueue.length > 0) {
    const batch = requestQueue.splice(0, 5);
    await Promise.all(batch.map(async date => {
      try {
        const dateKey = date.toISOString().split('T')[0];
        const data = await fetchDateSpecificData(date);
        if (data && data.timings) {
          newMonthlyData[dateKey] = data;
        } else {
          console.warn(`Invalid data for ${dateKey}:`, data);
        }
      } catch (error) {
        console.warn(`Date skipped (${date.toISOString().split('T')[0]}): ${error.message}`);
      }
    }));
  }
  
  STATE.monthlyData = newMonthlyData;
  DOM_ELEMENTS.chartLoading.style.display = 'none';
}

function prepareChartData(type) {
  const dates = Object.keys(STATE.monthlyData).sort();
  
  if (dates.length === 0) {
    console.warn('No dates available for chart data preparation');
    return { labels: [], datasets: [] };
  }
  
  const labels = dates.map(d => {
    const date = new Date(d);
    const day = date.getDate();
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    
    const dayOfMonth = date.getDate();
    if (dayOfMonth === 1 || dayOfMonth % 5 === 0) {
      return `${day} ${month}`;
    }
    return `${day}`;
  });
  
  const datasets = [];
  
  if (type === 'prayer' || type === 'combined') {
    PRAYER_CONFIG.order.forEach(prayer => {
      const data = dates.map(d => {
        const timeStr = STATE.monthlyData[d]?.timings?.[prayer];
        return timeStr && isValidTimeFormat(timeStr) 
          ? timeToMinutes(timeStr) 
          : null;
      });
      
      const validDataCount = data.filter(val => val !== null).length;
      if (validDataCount > 0) {
        datasets.push(createChartDataset(
          prayer, 
          PRAYER_CONFIG.names[prayer].color,
          data,
          'y'
        ));
      }
    });
  }
  
  if (type === 'temporal' || type === 'combined') {
    const dayHoursData = dates.map(d => {
      const timings = STATE.monthlyData[d]?.timings;
      if (!timings) return null;
      
      const fajrStr = timings.Fajr;
      const maghribStr = timings.Maghrib;
      
      if (!isValidTimeFormat(fajrStr) || !isValidTimeFormat(maghribStr)) return null;
      
      const fajr = timeToMinutes(fajrStr);
      const maghrib = timeToMinutes(maghribStr);
      const dayDuration = calculateDaytimeDuration(fajr, maghrib);
      return Math.round((dayDuration / 12) * 1000) / 1000;
    });
    
    const nightHoursData = dates.map(d => {
      const timings = STATE.monthlyData[d]?.timings;
      if (!timings) return null;
      
      const fajrStr = timings.Fajr;
      const maghribStr = timings.Maghrib;
      
      if (!isValidTimeFormat(fajrStr) || !isValidTimeFormat(maghribStr)) return null;
      
      const fajr = timeToMinutes(fajrStr);
      const maghrib = timeToMinutes(maghribStr);
      const dayDuration = calculateDaytimeDuration(fajr, maghrib);
      return Math.round(((1440 - dayDuration) / 12) * 1000) / 1000;
    });
    
    const axisID = type === 'combined' ? 'y1' : 'y';
    
    const validDayData = dayHoursData.filter(val => val !== null).length;
    const validNightData = nightHoursData.filter(val => val !== null).length;
    
    if (validDayData > 0) {
      datasets.push(createChartDataset(
        'Day Hours', 
        TEMPORAL_CONFIG.day.color, 
        dayHoursData,
        axisID
      ));
    }
    if (validNightData > 0) {
      datasets.push(createChartDataset(
        'Night Hours', 
        TEMPORAL_CONFIG.night.color, 
        nightHoursData,
        axisID
      ));
    }
  }
  
  return { labels, datasets };
}

function createChartDataset(label, color, data, axisID) {
  const isTemporal = label.includes('Hour');
  return {
    label,
    data,
    borderColor: color,
    backgroundColor: `${color}20`,
    borderWidth: 2,
    borderDash: isTemporal ? [5, 5] : [],
    fill: false,
    tension: 0.1,
    yAxisID: axisID,
    spanGaps: true,
    _precision: isTemporal ? 'fractional' : 'minute'
  };
}

function createChartConfiguration(type, chartData) {
  const allPrayerData = chartData.datasets
    .filter(ds => !ds.yAxisID || ds.yAxisID === 'y')
    .flatMap(ds => ds.data)
    .filter(val => val != null);
    
  const allTemporalData = chartData.datasets
    .filter(ds => ds.yAxisID === 'y1')
    .flatMap(ds => ds.data)
    .filter(val => val != null);

  const scales = {
    x: {
      grid: { 
        color: 'rgba(255,255,255,0.1)',
        drawOnChartArea: true
      },
      ticks: { 
        color: 'rgba(255,255,255,0.7)',
        maxTicksLimit: 15,
        font: { size: 11 }
      },
      title: {
        display: true,
        text: 'Date',
        color: 'rgba(255,255,255,0.8)',
        font: { size: 12}
      }
    },
    y: {
      grid: { 
        color: 'rgba(255,255,255,0.1)',
        drawOnChartArea: true
      },
      ticks: {
        color: 'rgba(255,255,255,0.7)',
        callback: type === 'temporal' ? (value => `${Math.round(value * 100) / 100} min`) : formatMinutesAsDisplayTime,
        stepSize: type === 'temporal' ? 10 : 60,
        font: { size: 11 }
      },
      title: {
        display: true,
        text: type === 'temporal' ? 'Duration (minutes)' : 'Time',
        color: 'rgba(255,255,255,0.8)',
        font: { size: 12 }
      }
    }
  };

  if (allPrayerData.length > 0) {
    const minPrayer = Math.min(...allPrayerData);
    const maxPrayer = Math.max(...allPrayerData);
    
    const minHour = Math.floor(minPrayer / 60) * 60;
    const maxHour = Math.ceil(maxPrayer / 60) * 60;
    
    scales.y.min = Math.max(0, minHour - 60);
    scales.y.max = Math.min(1440, maxHour + 60);
  }

  if (type === 'temporal') {
    const allData = chartData.datasets
      .flatMap(ds => ds.data)
      .filter(val => val != null);
      
    if (allData.length > 0) {
      const minValue = Math.min(...allData);
      const maxValue = Math.max(...allData);
      
      const minRounded = Math.floor(minValue / 5) * 5;
      const maxRounded = Math.ceil(maxValue / 5) * 5;
      
      scales.y.min = Math.max(0, minRounded - 10);
      scales.y.max = maxRounded + 10;
    }
  }

  if (type === 'combined') {
    scales.y1 = {
      position: 'right',
      grid: { display: false },
      ticks: {
        color: 'rgba(255,255,255,0.7)',
        callback: value => `${Math.round(value * 100) / 100} min`,
        stepSize: 10,
        font: { size: 11 }
      },
      title: {
        display: true,
        text: 'Temporal Hours (minutes)',
        color: 'rgba(255,255,255,0.8)',
        font: { size: 12}
      }
    };
    
    if (allTemporalData.length > 0) {
      const minTemporal = Math.min(...allTemporalData);
      const maxTemporal = Math.max(...allTemporalData);
      
      const minRounded = Math.floor(minTemporal / 5) * 5;
      const maxRounded = Math.ceil(maxTemporal / 5) * 5;
      
      scales.y1.min = Math.max(0, minRounded - 10);
      scales.y1.max = maxRounded + 10;
    }
  }

  return {
    responsive: true,
    maintainAspectRatio: false,
    scales,
    plugins: {
      legend: {
        position: 'top',
        align: 'center',
        labels: { 
          color: 'rgba(255,255,255,0.9)',
          font: { size: 12, weight: '500' },
          padding: 15
        }
      },
      tooltip: {
        backgroundColor: 'rgba(13, 27, 58, 0.95)',
        borderColor: 'rgba(168, 237, 234, 0.4)',
        borderWidth: 2,
        titleColor: '#a8edea',
        bodyColor: '#fff',
        titleFont: { size: 13, weight: 'bold' },
        bodyFont: { size: 12 },
        padding: 12,
        cornerRadius: 8,
        callbacks: createEnhancedTooltipCallbacks()
      }
    },
    elements: {
      point: {
        radius: 3,
        hoverRadius: 4,
        borderWidth: 2,
        hoverBorderWidth: 3
      },
      line: {
        borderWidth: 3,
        tension: 0.2
      }
    },
    interaction: {
      intersect: true,
      mode: 'nearest'
    }
  };
}

function timeToMinutes(timeString) {
  if (!timeString || typeof timeString !== 'string') return NaN;

  const cleanTime = timeString.replace(/\s*(AM|PM)\s*/i, '').trim();
  const isPM = timeString.toLowerCase().includes('pm');
  const isAM = timeString.toLowerCase().includes('am');

  const parts = cleanTime.split(':');
  if (parts.length < 2 || parts.length > 3) return NaN;

  let hours = parseInt(parts[0], 10);
  const minutes = parseInt(parts[1], 10);
  const seconds = parts.length > 2 ? parseInt(parts[2], 10) : 0;

  if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59 ||
      (parts.length > 2 && (isNaN(seconds) || seconds < 0 || seconds > 59))) {
    return NaN;
  }

  if (isPM && hours !== 12) hours += 12;
  if (isAM && hours === 12) hours = 0;

  return hours * 60 + minutes;
}

function minutesToTime(totalMinutes) {
  if (isNaN(totalMinutes)) return '';
  const normalizedMinutes = ((totalMinutes % 1440) + 1440) % 1440;
  const hours = Math.floor(normalizedMinutes / 60);
  const minutes = Math.floor(normalizedMinutes % 60);
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

function minutesToTimeWithSeconds(totalMinutes) {
  if (isNaN(totalMinutes)) return '';
  
  const normalizedMinutes = ((totalMinutes % 1440) + 1440) % 1440;
  const hours = Math.floor(normalizedMinutes / 60);
  const minutes = Math.floor(normalizedMinutes % 60);
  const seconds = Math.round((normalizedMinutes % 1) * 60);
  
  if (seconds > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

function isValidTimeFormat(timeString) {
  if (!timeString || typeof timeString !== 'string') return false;

  const basicFormat = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?(\s*(AM|PM))?$/i.test(timeString);
  if (!basicFormat) return false;

  return !isNaN(timeToMinutes(timeString));
}

function calculateDaytimeDuration(fajrMinutes, maghribMinutes) {
  if (isNaN(fajrMinutes) || isNaN(maghribMinutes)) return 0;
  
  if (maghribMinutes >= fajrMinutes) {
    return maghribMinutes - fajrMinutes;
  }
  
  return (1440 - fajrMinutes) + maghribMinutes;
}

function formatMinutesAsDisplayTime(totalMinutes) {
  if (isNaN(totalMinutes)) return '';
  
  const normalizedMinutes = ((totalMinutes % 1440) + 1440) % 1440;
  const hours24 = Math.floor(normalizedMinutes / 60);
  const minutes = Math.floor(normalizedMinutes % 60);
  
  const hours12 = hours24 === 0 ? 12 : (hours24 > 12 ? hours24 - 12 : hours24);
  const ampm = hours24 < 12 ? 'AM' : 'PM';
  
  return `${hours12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
}



function calculatePreciseTemporalHour(fajrMinutes, maghribMinutes, isNight = false) {
  if (isNaN(fajrMinutes) || isNaN(maghribMinutes)) return null;
  
  const dayDuration = calculateDaytimeDuration(fajrMinutes, maghribMinutes);
  const nightDuration = 1440 - dayDuration;
  
  return isNight ? nightDuration / 12 : dayDuration / 12;
}

function createEnhancedTooltipCallbacks() {
  return {
    title: context => {
      const dataIndex = context[0].dataIndex;
      const dates = Object.keys(STATE.monthlyData).sort();
      const date = new Date(dates[dataIndex]);
      return date.toLocaleDateString('en-US', { 
        weekday: 'short', 
        month: 'short', 
        day: 'numeric' 
      });
    },
    label: context => {
      const value = context.raw;
      const label = context.dataset.label;
      
      if (label.includes('Hour')) {
        const totalMinutes = value;
        const hours = Math.floor(totalMinutes / 60);
        const remainingMinutes = totalMinutes % 60;
        const seconds = Math.round((remainingMinutes % 1) * 60);
        const wholeMinutes = Math.floor(remainingMinutes);
        
        let timeDisplay = `${totalMinutes.toFixed(3)} min`;
        if (hours > 0) {
          timeDisplay += ` (${hours}h ${wholeMinutes}m`;
          if (seconds > 0) {
            timeDisplay += ` ${seconds}s`;
          }
          timeDisplay += ')';
        } else if (seconds > 0) {
          timeDisplay += ` (${wholeMinutes}m ${seconds}s)`;
        }
        
        return `${label}: ${timeDisplay}`;
      } else {
        return `${label}: ${formatMinutesAsDisplayTime(value)}`;
      }
    },
    afterBody: context => {
      if (context.some(c => c.dataset.label.includes('Hour'))) {
        return [
          '',
          'Temporal Hours:',
          'Day hours: Fajr to Maghrib ÷ 12',
          'Night hours: Maghrib to Fajr ÷ 12'
        ];
      }
      return [];
    }
  };
}

function clearChartErrors() {
  const chartContainer = DOM_ELEMENTS.chartCanvas?.parentElement;
  if (chartContainer) {
    const existingError = chartContainer.querySelector('.chart-error');
    if (existingError) existingError.remove();
  }
}

function destroyExistingChart() {
  if (STATE.chart) {
    try {
      STATE.chart.destroy();
    } catch (error) {
      console.warn('Error destroying existing chart:', error);
    }
    STATE.chart = null;
  }
}

function validateChartData(chartData) {
  if (!chartData || !chartData.datasets || chartData.datasets.length === 0) {
    console.warn('No valid chart data available');
    return false;
  }
  return true;
}

function getChartContext() {
  if (!DOM_ELEMENTS.chartCanvas) {
    console.error('Chart canvas element not found');
    return null;
  }

  const ctx = DOM_ELEMENTS.chartCanvas.getContext('2d');
  if (!ctx) {
    console.error('Failed to get canvas context');
    return null;
  }
  return ctx;
}

function showChartError() {
  const chartContainer = DOM_ELEMENTS.chartCanvas.parentElement;
  if (chartContainer && !chartContainer.querySelector('.chart-error')) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'chart-error';
    errorDiv.style.cssText = 'text-align: center; color: rgba(255,255,255,0.6); font-size: 0.9rem; padding: 20px;';
    errorDiv.textContent = 'Chart temporarily unavailable';
    chartContainer.appendChild(errorDiv);
  }
}

function renderChart(type) {
  if (!type || !['prayer', 'temporal', 'combined'].includes(type)) {
    console.error('Invalid chart type:', type);
    return;
  }

  STATE.chartType = type;
  updateActiveChartControl();

  clearChartErrors();

  destroyExistingChart();

  if (Object.keys(STATE.monthlyData).length === 0) {
    console.warn('No monthly data available for chart rendering');
    return;
  }
  
  try {
    const chartData = prepareChartData(type);
    
    if (!validateChartData(chartData)) return;

    const ctx = getChartContext();
    if (!ctx) return;
    
    STATE.chart = new Chart(ctx, {
      type: 'line',
      data: chartData,
      options: createChartConfiguration(type, chartData)
    });
  } catch (error) {
    console.error('Chart rendering failed:', error);
    DOM_ELEMENTS.chartLoading.style.display = 'none';
    showChartError();
  }
}


async function reverseGeocode(lat, lng) {
  const cacheKey = `geocode_${lat}_${lng}`;
  const cached = API_CACHE.get(cacheKey);
  if (cached) return cached;
  
  if (Date.now() - STATE.lastGeocodeRequest < 1000) {
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  STATE.lastGeocodeRequest = Date.now();
  
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 5000);
  
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`,
      {
        signal: controller.signal,
        headers: {
          'User-Agent': 'IslamicPrayerTimesApp/1.0 (<EMAIL>)',
          'Accept-Language': navigator.language || 'en-US'
        }
      }
    );
    
    clearTimeout(timeoutId);
    
    if (!response.ok) throw new Error(`HTTP ${response.status}`);
    
    const data = await response.json();
    const address = data.address || {};
    
    const locationName = [
      address.city || address.town || address.village || address.county,
      address.country
    ].filter(Boolean).join(', ') || 'Current Location';
    
    API_CACHE.set(cacheKey, locationName);
    return locationName;
    
  } catch (error) {
    clearTimeout(timeoutId);
    throw new Error('Location name unavailable');
  }
}

async function handleLocationSuccess(position) {
  STATE.location = {
    lat: position.coords.latitude,
    lng: position.coords.longitude
  };

  try {
    const locationName = await reverseGeocode(position.coords.latitude, position.coords.longitude);
    STATE.locationName = locationName;
    updateLocationInputs(locationName);
  } catch {
    STATE.locationName = `${position.coords.latitude.toFixed(4)}, ${position.coords.longitude.toFixed(4)}`;
  }

  fetchPrayerData();
  DOM_ELEMENTS.getLocationBtn.disabled = false;
}

function handleLocationError(error) {
  const messages = {
    1: 'Location access denied',
    2: 'Location unavailable',
    3: 'Location request timed out'
  };
  showStatusMessage(messages[error.code] || 'Location error');
  DOM_ELEMENTS.getLocationBtn.disabled = false;
}

function updateLocationInputs(locationName) {
  const cityInput = document.getElementById('city');
  const countryInput = document.getElementById('country');
  if (cityInput) cityInput.value = locationName.split(',')[0] || '';
  if (countryInput) countryInput.value = locationName.split(',')[1]?.trim() || '';
}

async function fetchUserLocation() {
  showStatusMessage('Detecting location...');
  DOM_ELEMENTS.getLocationBtn.disabled = true;

  if (!navigator.geolocation) {
    showStatusMessage('Geolocation not supported');
    DOM_ELEMENTS.getLocationBtn.disabled = false;
    return;
  }

  navigator.geolocation.getCurrentPosition(
    handleLocationSuccess,
    handleLocationError,
    { timeout: 10000, enableHighAccuracy: true }
  );
}

function setupEventListeners() {
  document.getElementById('calculateBtn').addEventListener('click', fetchPrayerData);
  DOM_ELEMENTS.getLocationBtn.addEventListener('click', fetchUserLocation);
  
  ['btn-prayer-times', 'btn-temporal-hours', 'btn-combined'].forEach(btnId => {
    const btn = document.getElementById(btnId);
    if (btn) {
      btn.addEventListener('click', () => {
        const typeMap = {
          'btn-prayer-times': 'prayer',
          'btn-temporal-hours': 'temporal',
          'btn-combined': 'combined'
        };
        renderChart(typeMap[btnId]);
      });
    }
  });
}

function setupNetworkListeners() {
  window.addEventListener('online', () => {
    if (DOM_ELEMENTS.error.textContent.includes('offline')) {
      DOM_ELEMENTS.error.style.display = 'none';
    }
  });
  
  window.addEventListener('offline', () => {
    showStatusMessage(STATE.prayerTimes ? 
      'Showing cached data (offline)' : 
      'Connect to load prayer times'
    );
  });
}

function initializeApplication() {
  setupEventListeners();
  setupNetworkListeners();

  if (typeof Chart === 'undefined') {
    console.warn('Chart.js library not loaded - charts will not be available');
  }

  // Add debug function to global scope for testing
  window.debugHijriDate = async function() {
    console.log('=== Hijri Date Debug ===');
    console.log('Timezone:', STATE.locationTimeZone || STATE.userTimeZone);
    console.log('Maghrib time:', STATE.prayerTimes?.Maghrib);
    console.log('Current Hijri date:', DOM_ELEMENTS.hijriDate?.textContent);
    console.log('=== End Debug ===');
  };

  const city = getInputValue('city');
  const country = getInputValue('country');
  if (city && country) {
    STATE.locationName = `${city}, ${country}`;
    fetchPrayerData();
  }
}

document.addEventListener('DOMContentLoaded', initializeApplication);
</script>

<!-- Footer -->
<footer class="footer">
    <div class="container">
        <p class="footer-text">Powered by <a href="https://aladhan.com/prayer-times-api" target="_blank" rel="noopener noreferrer">Aladhan API</a></p>
    </div>
</footer>

<!-- Close the document -->
</body>
</html>