<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test - Muslim Prayer Times</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            background: #ffebee;
            border-left-color: #f44336;
            color: #c62828;
        }
        .loading {
            background: #e3f2fd;
            border-left-color: #2196F3;
            color: #1565c0;
        }
        .proxy-status {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
            font-size: 14px;
        }
        .proxy-success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .proxy-failed {
            background: #ffebee;
            color: #c62828;
        }
        .proxy-trying {
            background: #fff3e0;
            color: #ef6c00;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕌 Muslim Prayer Times - CORS Test</h1>
        <p>This page tests the CORS handling improvements for the Muslim Prayer Times application.</p>
        
        <div class="input-group">
            <label for="city">City:</label>
            <input type="text" id="city" value="Mecca" placeholder="Enter city name">
        </div>
        
        <div class="input-group">
            <label for="country">Country:</label>
            <input type="text" id="country" value="Saudi Arabia" placeholder="Enter country name">
        </div>
        
        <div class="input-group">
            <label for="method">Calculation Method:</label>
            <select id="method">
                <option value="4">Umm Al-Qura University, Makkah</option>
                <option value="3">Muslim World League</option>
                <option value="2">Islamic Society of North America</option>
                <option value="5">Egyptian General Authority of Survey</option>
                <option value="7">Institute of Geophysics, University of Tehran</option>
            </select>
        </div>
        
        <button id="testBtn" onclick="testCorsHandling()">🔄 Test CORS Handling</button>
        
        <div id="results" class="results" style="display: none;">
            <h3>Results:</h3>
            <div id="output"></div>
        </div>
    </div>

    <script>
        // Copy the improved CORS handling from Muslim Prayer Times.txt
        const API_CONFIG = {
            BASE_URL: 'https://api.aladhan.com/v1/',
            CORS_PROXIES: [
                '', // Try direct first
                'https://api.allorigins.win/raw?url=',
                'https://corsproxy.io/?',
                'https://cors.bridged.cc/',
                'https://api.codetabs.com/v1/proxy?quest=',
                'https://thingproxy.freeboard.io/fetch/'
            ],
            RETRY_ATTEMPTS: 3,
            RETRY_DELAY: 1000
        };

        const REQUEST_TIMEOUT_MS = 10000;

        function showResults(content, type = 'results') {
            const resultsDiv = document.getElementById('results');
            const outputDiv = document.getElementById('output');
            
            resultsDiv.style.display = 'block';
            resultsDiv.className = `results ${type}`;
            outputDiv.innerHTML = content;
        }

        function addProxyStatus(message, status) {
            const outputDiv = document.getElementById('output');
            const statusDiv = document.createElement('div');
            statusDiv.className = `proxy-status proxy-${status}`;
            statusDiv.textContent = message;
            outputDiv.appendChild(statusDiv);
        }

        async function tryWithCorsProxies(originalUrl) {
            const errors = [];
            
            for (let i = 1; i < API_CONFIG.CORS_PROXIES.length; i++) {
                const proxy = API_CONFIG.CORS_PROXIES[i];
                let proxiedUrl;
                
                // Handle different proxy URL formats
                if (proxy.includes('allorigins.win')) {
                    proxiedUrl = proxy + encodeURIComponent(originalUrl);
                } else if (proxy.includes('corsproxy.io')) {
                    proxiedUrl = proxy + encodeURIComponent(originalUrl);
                } else if (proxy.includes('cors.bridged.cc')) {
                    proxiedUrl = proxy + originalUrl;
                } else if (proxy.includes('codetabs.com')) {
                    proxiedUrl = proxy + encodeURIComponent(originalUrl);
                } else {
                    proxiedUrl = proxy + encodeURIComponent(originalUrl);
                }

                try {
                    addProxyStatus(`Trying proxy ${i}: ${proxy}`, 'trying');
                    
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT_MS);
                    
                    const response = await fetch(proxiedUrl, {
                        method: 'GET',
                        signal: controller.signal,
                        headers: {
                            'Accept': 'application/json'
                        }
                    });
                    
                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    addProxyStatus(`✅ Proxy ${i} succeeded!`, 'success');
                    return data;
                } catch (error) {
                    const errorMsg = `❌ Proxy ${i} failed: ${error.message}`;
                    addProxyStatus(errorMsg, 'failed');
                    errors.push(errorMsg);
                    
                    if (i === API_CONFIG.CORS_PROXIES.length - 1) {
                        throw new Error(`All CORS proxies failed:\n${errors.join('\n')}`);
                    }
                    
                    // Brief delay before trying next proxy
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
        }

        async function makeDirectApiRequest(url) {
            try {
                addProxyStatus('Trying direct API request...', 'trying');
                
                const response = await fetch(url, {
                    headers: {
                        'Accept': 'application/json'
                    },
                    method: 'GET'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                addProxyStatus('✅ Direct API request succeeded!', 'success');
                return data;
            } catch (error) {
                addProxyStatus(`❌ Direct request failed: ${error.message}`, 'failed');
                
                if (error.message.includes('CORS') || error.message.includes('preflight')) {
                    throw new Error('CORS policy blocked the request');
                } else if (error.message.includes('Failed to fetch')) {
                    throw new Error('Network error or CORS issue');
                }
                throw error;
            }
        }

        async function attemptApiRequest(url) {
            try {
                return await makeDirectApiRequest(url);
            } catch (directError) {
                // If it's a CORS error, try CORS proxies
                if (directError.message.includes('CORS') || directError.message.includes('Failed to fetch')) {
                    addProxyStatus('Direct request failed, trying CORS proxies...', 'trying');
                    return await tryWithCorsProxies(url);
                }
                throw directError;
            }
        }

        async function testCorsHandling() {
            const testBtn = document.getElementById('testBtn');
            const city = document.getElementById('city').value.trim();
            const country = document.getElementById('country').value.trim();
            const method = document.getElementById('method').value;

            if (!city || !country) {
                showResults('Please enter both city and country.', 'error');
                return;
            }

            testBtn.disabled = true;
            testBtn.textContent = '🔄 Testing...';
            
            showResults('Starting CORS test...', 'loading');

            try {
                const url = `https://api.aladhan.com/v1/timingsByCity?city=${encodeURIComponent(city)}&country=${encodeURIComponent(country)}&method=${method}`;
                
                addProxyStatus(`Testing URL: ${url}`, 'trying');
                
                const data = await attemptApiRequest(url);
                
                if (data && data.data && data.data.timings) {
                    const timings = data.data.timings;
                    let resultHtml = '<h4>✅ Success! Prayer times retrieved:</h4><ul>';
                    
                    ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'].forEach(prayer => {
                        if (timings[prayer]) {
                            resultHtml += `<li><strong>${prayer}:</strong> ${timings[prayer]}</li>`;
                        }
                    });
                    
                    resultHtml += '</ul>';
                    resultHtml += `<p><strong>Location:</strong> ${data.data.meta?.city || city}, ${data.data.meta?.country || country}</p>`;
                    resultHtml += `<p><strong>Method:</strong> ${data.data.meta?.method?.name || 'Unknown'}</p>`;
                    
                    showResults(resultHtml, 'results');
                } else {
                    showResults('❌ Invalid response format received.', 'error');
                }
                
            } catch (error) {
                console.error('Test failed:', error);
                showResults(`❌ Test failed: ${error.message}`, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🔄 Test CORS Handling';
            }
        }

        // Auto-detect file protocol and show warning
        if (window.location.protocol === 'file:') {
            const warning = document.createElement('div');
            warning.style.cssText = `
                background: #ff9800; color: white; padding: 10px; margin-bottom: 20px;
                border-radius: 5px; text-align: center;
            `;
            warning.innerHTML = `
                <strong>⚠️ File Protocol Detected</strong><br>
                You're running this from a local file. CORS proxies will be used automatically.
            `;
            document.body.insertBefore(warning, document.body.firstChild);
        }
    </script>
</body>
</html>
